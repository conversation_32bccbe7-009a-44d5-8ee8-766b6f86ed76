<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.partskick</groupId>
    <artifactId>parts-kick</artifactId>
    <version>2.3.0</version>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mainclass>com.partskick.gui.App</mainclass>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.partskick</groupId>
            <artifactId><PERSON><PERSON><PERSON>mon</artifactId>
            <version>3.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>6.1.11</version>
        </dependency>
        <dependency>
            <groupId>com.github.albfernandez</groupId>
            <artifactId>javadbf</artifactId>
            <version>1.14.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.8.1</version>
        </dependency>
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core-java11</artifactId>
            <version>6.4.0</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.15</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.4.5</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.26</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.14.1</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.14.1</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>2.14.1</version>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.8.0</version>
        </dependency>
        <dependency>
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports</artifactId>
            <version>6.20.1</version>
        </dependency>
        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>semver4j</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>RELEASE</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.yworks</groupId>
            <artifactId>yguard</artifactId>
            <version>4.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.yworks</groupId>
            <artifactId>annotation</artifactId>
            <version>4.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.github.lgooddatepicker</groupId>
            <artifactId>LGoodDatePicker</artifactId>
            <version>11.2.1</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-web</artifactId>
            <version>16</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-swing</artifactId>
            <version>16</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.30</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <!-- Build an executable JAR -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <archive>
                        <manifest>
                            <mainClass>com.partskick.gui.App</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.2.4</version>
                <configuration>
                    <filters>
                        <filter>
                            <artifact>*:*</artifact>
                            <excludes>
                                <exclude>META-INF/*.SF</exclude>
                                <exclude>META-INF/*.DSA</exclude>
                                <exclude>META-INF/*.RSA</exclude>
                            </excludes>
                        </filter>
                    </filters>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <id>obfuscate</id>
                        <configuration>
                            <target>
                                <property
                                        name="project.build.prefix"
                                        value="${project.build.directory}/${project.build.finalName}"/>
                                <property
                                        name="project.jar"
                                        value="${project.build.prefix}.jar"/>

                                <!--
                                  check if the project jar / project build artefact already
                                  exists
                                  -->
                                <available
                                        file="${project.jar}"
                                        property="project.jar.exists"/>
                                <fail unless="project.jar.exists">${project.jar} does not exist.</fail>

                                <!-- backup the project jar -->
                                <property
                                        name="project.jar.unobf"
                                        value="${project.build.prefix}_unobf.jar"/>
                                <move
                                        file="${project.jar}"
                                        tofile="${project.jar.unobf}"
                                        verbose="true"/>

                                <!-- extract the project classes for obfuscation -->
                                <property
                                        name="project.classes.unobf"
                                        value="${project.build.prefix}_classes_unobf.jar"/>
                                <jar destfile="${project.classes.unobf}">
                                    <mappedresources>
                                        <zipfileset src="${project.jar.unobf}">
                                            <include name="com/partskick/**"/>
                                        </zipfileset>
                                        <mapper
                                                type="glob"
                                                from="com/partskick/*"
                                                to="*"/>
                                    </mappedresources>
                                </jar>

                                <!-- obfuscate the project classes -->
                                <property
                                        name="runtime.classpath"
                                        refid="maven.runtime.classpath"/>
                                <property
                                        name="project.classes.obf"
                                        value="${project.build.prefix}_classes_obf.jar"/>
                                <taskdef
                                        name="yguard"
                                        classname="com.yworks.yguard.YGuardTask"
                                        classpathref="maven.test.classpath"/>
                                <yguard>
                                    <inoutpair
                                            in="${project.classes.unobf}"
                                            out="${project.classes.obf}"/>

                                    <externalclasses>
                                        <pathelement path="${runtime.classpath}"/>
                                    </externalclasses>

                                    <rename
                                            mainclass="${mainclass}"
                                            logfile="${project.build.directory}/yguard.log.xml">

                                    <property
                                                name="error-checking"
                                                value="pedantic"/>
                                    </rename>
                                </yguard>

                                <!--
                                  recreate the project jar / project build artefact
                                  use the zip task for easy copying of the manifest file from
                                  the original project jar / project build artefact
                                  -->
                                <zip
                                        compress="false"
                                        destfile="${project.jar}">
                                    <zipfileset src="${project.jar.unobf}">
                                        <include name="**"/>
                                        <exclude name="com/partskick/**"/>
                                    </zipfileset>
                                    <zipfileset
                                            src="${project.classes.obf}"
                                    >
                                        <include name="**"/>
                                        <exclude name="META-INF/**"/>
                                    </zipfileset>
                                </zip>
                                <copy file="${project.jar}" tofile="./dist/pk.jar" force="true" overwrite="true"/>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>



<!--
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <id>obfuscate</id>
                        <configuration>
                            <target>
                                <property name="runtime_classpath" refid="maven.runtime.classpath"/>
                                <taskdef name="yguard" classname="com.yworks.yguard.YGuardTask" classpath="${runtime_classpath}"/>
                                <yguard>
                                    <inoutpair in="${project.build.directory}/${project.build.finalName}.jar" out="${project.build.directory}/${project.build.finalName}_obfuscated.jar" />
                                    <shrink logfile="${project.build.directory}/yshrink.log.xml">
                                        <keep>
                                            &lt;!&ndash; main method &ndash;&gt;
                                            <method name="void main(java.lang.String[])" class="${mainclass}" />
                                        </keep>
                                    </shrink>

                                    <rename mainclass="${mainclass}" logfile="${project.build.directory}/yguard.log.xml">
                                        <property name="error-checking" value="pedantic"/>
                                    </rename>
                                </yguard>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
-->
        </plugins>
    </build>
</project>