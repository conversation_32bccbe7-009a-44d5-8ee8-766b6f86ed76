use GraalVM to build native image for Java
Install GraalVM, Visual Studio, VC

"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"

##### "D:\devtools\jdk-17.0.2\bin\java" -agentlib:native-image-agent=config-output-dir=../config -jar ../target/parts-kick-1.0.jar
"D:\devtools\jdk-17.0.2\bin\java" -agentlib:native-image-agent=config-merge-dir=../config -jar ../target/parts-kick-1.0.jar 1
"D:\devtools\jdk-17.0.2\bin\java" -agentlib:native-image-agent=config-merge-dir=../config -jar ../target/parts-kick-1.0.jar 2


D:\devtools\graalvm-ce-java17-22.3.1\bin\native-image -H:ConfigurationFileDirectories=../config --allow-incomplete-classpath --link-at-build-time -jar ../target/parts-kick-1.0.jar PartsKick

EDITBIN /SUBSYSTEM:WINDOWS PartsKick.exe

PartsKick.exe -Djava.home=.
