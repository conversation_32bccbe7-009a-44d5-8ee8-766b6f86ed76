[{"name": "[B"}, {"name": "[C"}, {"name": "[D"}, {"name": "[F"}, {"name": "[I"}, {"name": "[J"}, {"name": "[Lcom.sun.jna.platform.win32.WinNT$GROUP_AFFINITY;"}, {"name": "[Lcom.sun.jna.platform.win32.WinNT$PROCESSOR_GROUP_INFO;"}, {"name": "[Ljava.awt.event.MouseMotionListener;"}, {"name": "[Ljava.io.File;"}, {"name": "[Ljava.lang.<PERSON>;"}, {"name": "[Ljava.lang.Byte;"}, {"name": "[Ljava.lang.Character;"}, {"name": "[Ljava.lang.Class;"}, {"name": "[Ljava.lang.Double;"}, {"name": "[Ljava.lang.Float;"}, {"name": "[Ljava.lang.Integer;"}, {"name": "[Ljava.lang.Long;"}, {"name": "[Ljava.lang.Short;"}, {"name": "[Ljava.lang.String;"}, {"name": "[Ljava.math.BigDecimal;"}, {"name": "[Ljava.math.BigInteger;"}, {"name": "[Ljava.net.URL;"}, {"name": "[Ljava.sql.Date;"}, {"name": "[Ljava.sql.Time;"}, {"name": "[Ljava.sql.Timestamp;"}, {"name": "[Ljava.util.Calendar;"}, {"name": "[Ljava.util.Date;"}, {"name": "[Lsun.security.pkcs.SignerInfo;"}, {"name": "[S"}, {"name": "[Z"}, {"name": "ch.qos.logback.classic.PatternLayout", "queryAllPublicMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.encoder.PatternLayoutEncoder", "queryAllPublicMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.DateConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.LevelConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.LineSeparatorConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.LoggerConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.MessageConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.ThreadConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.core.Console<PERSON>ppender", "queryAllPublicMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.core.OutputStreamAppender", "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["ch.qos.logback.core.encoder.Encoder"]}, {"name": "setLayout", "parameterTypes": ["ch.qos.logback.core.Layout"]}]}, {"name": "ch.qos.logback.core.encoder.LayoutWrappingEncoder", "methods": [{"name": "setParent", "parameterTypes": ["ch.qos.logback.core.spi.ContextAware"]}]}, {"name": "ch.qos.logback.core.pattern.PatternLayoutBase", "methods": [{"name": "setPattern", "parameterTypes": ["java.lang.String"]}]}, {"name": "ch.qos.logback.core.pattern.PatternLayoutEncoderBase", "methods": [{"name": "setPattern", "parameterTypes": ["java.lang.String"]}]}, {"name": "ch.qos.logback.core.rolling.RollingFileAppender", "queryAllPublicMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setFile", "parameterTypes": ["java.lang.String"]}, {"name": "setRollingPolicy", "parameterTypes": ["ch.qos.logback.core.rolling.RollingPolicy"]}]}, {"name": "ch.qos.logback.core.rolling.RollingPolicyBase", "methods": [{"name": "setFileNamePattern", "parameterTypes": ["java.lang.String"]}, {"name": "setParent", "parameterTypes": ["ch.qos.logback.core.FileAppender"]}]}, {"name": "ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy", "queryAllPublicMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setMaxFileSize", "parameterTypes": ["ch.qos.logback.core.util.FileSize"]}]}, {"name": "ch.qos.logback.core.rolling.TimeBasedRollingPolicy", "methods": [{"name": "setMaxHistory", "parameterTypes": ["int"]}, {"name": "setTotalSizeCap", "parameterTypes": ["ch.qos.logback.core.util.FileSize"]}]}, {"name": "ch.qos.logback.core.rolling.helper.DateTokenConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.core.rolling.helper.IntegerTokenConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.core.util.FileSize", "methods": [{"name": "valueOf", "parameterTypes": ["java.lang.String"]}]}, {"name": "com.fasterxml.jackson.databind.ext.Java7HandlersImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.fasterxml.jackson.databind.ext.Java7SupportImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.lowagie.text.pdf.PdfContentByte", "methods": [{"name": "showText", "parameterTypes": ["java.awt.font.GlyphVector"]}]}, {"name": "com.lowagie.text.pdf.PdfName", "allDeclaredFields": true}, {"name": "com.partskick.common.EmsSource", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setExportPath", "parameterTypes": ["java.lang.String"]}, {"name": "setName", "parameterTypes": ["java.lang.String"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["java.lang.String"]}]}, {"name": "com.partskick.common.PartsReplaceType", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setReplaceParts", "parameterTypes": ["java.lang.String"]}, {"name": "setReplaceType", "parameterTypes": ["int"]}, {"name": "setReplaceWith", "parameterTypes": ["java.lang.String"]}]}, {"name": "com.partskick.common.PartsType", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setDesc", "parameterTypes": ["java.lang.String"]}, {"name": "setDiscount", "parameterTypes": ["java.lang.Double"]}, {"name": "setEx<PERSON><PERSON><PERSON><PERSON>get", "parameterTypes": ["boolean"]}, {"name": "setExcluded", "parameterTypes": ["boolean"]}, {"name": "setName", "parameterTypes": ["java.lang.String"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["boolean"]}, {"name": "setTargetPrefix", "parameterTypes": ["java.lang.String"]}, {"name": "setType", "parameterTypes": ["java.lang.String"]}]}, {"name": "com.partskick.common.PartsTypeExtraDisc", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setDiscount", "parameterTypes": ["java.lang.Double"]}, {"name": "setPartsType", "parameterTypes": ["java.lang.String"]}]}, {"name": "com.partskick.common.PartsTypeTieredDisc", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setPartsType", "parameterTypes": ["java.lang.String"]}, {"name": "setTieredDisc", "parameterTypes": ["java.util.LinkedList"]}]}, {"name": "com.partskick.common.Tier", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setDiscIncrease", "parameterTypes": ["java.lang.Double"]}, {"name": "setStepAmount", "parameterTypes": ["java.lang.Double"]}]}, {"name": "com.partskick.model.Client", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setAddress1", "parameterTypes": ["java.lang.String"]}, {"name": "setAddress2", "parameterTypes": ["java.lang.String"]}, {"name": "setCity", "parameterTypes": ["java.lang.String"]}, {"name": "setClientName", "parameterTypes": ["java.lang.String"]}, {"name": "set<PERSON>ont<PERSON><PERSON>erson", "parameterTypes": ["java.lang.String"]}, {"name": "setEmail", "parameterTypes": ["java.lang.String"]}, {"name": "setPhone", "parameterTypes": ["java.lang.String"]}, {"name": "setPostCode", "parameterTypes": ["java.lang.String"]}, {"name": "setProvince", "parameterTypes": ["java.lang.String"]}]}, {"name": "com.partskick.model.LineItem", "queryAllPublicMethods": true, "methods": [{"name": "getAmtAfterDisc", "parameterTypes": []}, {"name": "getDescription", "parameterTypes": []}, {"name": "getLineNum", "parameterTypes": []}, {"name": "getPartsNum", "parameterTypes": []}, {"name": "getPartsTypeName", "parameterTypes": []}, {"name": "get<PERSON>ty", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.AESCipher$General", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.ARCFOURCipher", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.ChaCha20Cipher$ChaCha20Poly1305", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.DESCipher", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.DESedeCipher", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.DHParameters", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.GaloisCounterMode$AESGCM", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.TlsKeyMaterialGenerator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.TlsMasterSecretGenerator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.TlsPrfGenerator$V12", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.CallbackProxy", "methods": [{"name": "callback", "parameterTypes": ["java.lang.Object[]"]}]}, {"name": "com.sun.jna.NativeLong", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.Union", "allDeclaredFields": true}, {"name": "com.sun.jna.platform.win32.BaseTSD$DWORD_PTR", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.BaseTSD$ULONG_PTR", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.COM.Dispatch", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.COM.Dispatch$ByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.COM.Unknown", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.COM.Unknown$ByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.COM.Wbemcli$IWbemServices", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.Guid$CLSID", "allDeclaredFields": true}, {"name": "com.sun.jna.platform.win32.Guid$GUID", "allDeclaredFields": true, "allPublicFields": true}, {"name": "com.sun.jna.platform.win32.OaIdl$CURRENCY", "allDeclaredFields": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["com.sun.jna.Pointer"]}]}, {"name": "com.sun.jna.platform.win32.OaIdl$CURRENCY$_CURRENCY", "allDeclaredFields": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["com.sun.jna.Pointer"]}]}, {"name": "com.sun.jna.platform.win32.OaIdl$DATE", "allDeclaredFields": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.OaIdl$DECIMAL", "allDeclaredFields": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["com.sun.jna.Pointer"]}]}, {"name": "com.sun.jna.platform.win32.OaIdl$DECIMAL$_DECIMAL1", "allDeclaredFields": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["com.sun.jna.Pointer"]}]}, {"name": "com.sun.jna.platform.win32.OaIdl$DECIMAL$_DECIMAL1$_DECIMAL1_DECIMAL", "allDeclaredFields": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["com.sun.jna.Pointer"]}]}, {"name": "com.sun.jna.platform.win32.OaIdl$DECIMAL$_DECIMAL2", "allDeclaredFields": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["com.sun.jna.Pointer"]}]}, {"name": "com.sun.jna.platform.win32.OaIdl$DECIMAL$_DECIMAL2$_DECIMAL2_DECIMAL", "allDeclaredFields": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["com.sun.jna.Pointer"]}]}, {"name": "com.sun.jna.platform.win32.OaIdl$VARIANT_BOOL", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.OaIdl$VARIANT_BOOLByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.OaIdl$_VARIANT_BOOLByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.Variant$VARIANT", "allDeclaredFields": true}, {"name": "com.sun.jna.platform.win32.Variant$VARIANT$ByReference", "allDeclaredFields": true}, {"name": "com.sun.jna.platform.win32.Variant$VARIANT$_VARIANT", "allDeclaredFields": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["com.sun.jna.Pointer"]}]}, {"name": "com.sun.jna.platform.win32.Variant$VARIANT$_VARIANT$__VARIANT", "allDeclaredFields": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["com.sun.jna.Pointer"]}]}, {"name": "com.sun.jna.platform.win32.Variant$VARIANT$_VARIANT$__VARIANT$BRECORD", "allDeclaredFields": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["com.sun.jna.Pointer"]}]}, {"name": "com.sun.jna.platform.win32.WTypes$BSTR", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WTypes$BSTRByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WTypes$VARTYPE", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinBase$SYSTEM_INFO", "allDeclaredFields": true}, {"name": "com.sun.jna.platform.win32.WinBase$SYSTEM_INFO$PI", "allDeclaredFields": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinBase$SYSTEM_INFO$UNION", "allDeclaredFields": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$BYTE", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$CHAR", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$CHARByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$DWORD", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$DWORDByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$LONG", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$LONGByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$LONGLONG", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$LONGLONGByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$PVOID", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$SCODE", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$SCODEByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$SHORT", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$UINT", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$UINTByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$ULONG", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$ULONGByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$ULONGLONG", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$ULONGLONGByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$USHORT", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$USHORTByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinDef$WORD", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinNT$CACHE_RELATIONSHIP", "allDeclaredFields": true}, {"name": "com.sun.jna.platform.win32.WinNT$GROUP_AFFINITY", "allDeclaredFields": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["com.sun.jna.Pointer"]}]}, {"name": "com.sun.jna.platform.win32.WinNT$GROUP_RELATIONSHIP", "allDeclaredFields": true}, {"name": "com.sun.jna.platform.win32.WinNT$HRESULT", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinNT$NUMA_NODE_RELATIONSHIP", "allDeclaredFields": true}, {"name": "com.sun.jna.platform.win32.WinNT$OSVERSIONINFOEX", "allDeclaredFields": true}, {"name": "com.sun.jna.platform.win32.WinNT$PROCESSOR_GROUP_INFO", "allDeclaredFields": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["com.sun.jna.Pointer"]}]}, {"name": "com.sun.jna.platform.win32.WinNT$PROCESSOR_RELATIONSHIP", "allDeclaredFields": true}, {"name": "com.sun.jna.platform.win32.WinNT$SYSTEM_LOGICAL_PROCESSOR_INFORMATION_EX", "allDeclaredFields": true}, {"name": "com.sun.jna.platform.win32.WinReg$HKEY", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.platform.win32.WinReg$HKEYByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.ptr.ByteByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.ptr.DoubleByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.ptr.FloatByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.ptr.IntByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.ptr.PointerByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.ptr.ShortByReference", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.jna.win32.DL<PERSON><PERSON><PERSON>"}, {"name": "com.sun.org.apache.xerces.internal.impl.dv.xs.ExtendedSchemaDVFactoryImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.org.apache.xerces.internal.impl.dv.xs.SchemaDVFactoryImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.org.apache.xerces.internal.jaxp.SAXParserFactoryImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.org.apache.xerces.internal.util.XMLGrammarPoolImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "java.awt.Font", "methods": [{"name": "textRequiresLayout", "parameterTypes": ["char[]", "int", "int"]}]}, {"name": "java.awt.Image"}, {"name": "java.awt.SequencedEvent"}, {"name": "java.awt.event.KeyEvent", "fields": [{"name": "VK_A"}, {"name": "VK_BACK_SLASH"}, {"name": "VK_BACK_SPACE"}, {"name": "VK_C"}, {"name": "VK_CONTEXT_MENU"}, {"name": "VK_COPY"}, {"name": "VK_CUT"}, {"name": "VK_DELETE"}, {"name": "VK_DOWN"}, {"name": "VK_END"}, {"name": "VK_ENTER"}, {"name": "VK_ESCAPE"}, {"name": "VK_F10"}, {"name": "VK_F2"}, {"name": "VK_F5"}, {"name": "VK_F8"}, {"name": "VK_H"}, {"name": "VK_HOME"}, {"name": "VK_INSERT"}, {"name": "VK_KP_DOWN"}, {"name": "VK_KP_LEFT"}, {"name": "VK_KP_RIGHT"}, {"name": "VK_KP_UP"}, {"name": "VK_LEFT"}, {"name": "VK_O"}, {"name": "VK_PAGE_DOWN"}, {"name": "VK_PAGE_UP"}, {"name": "VK_PASTE"}, {"name": "VK_RIGHT"}, {"name": "VK_SLASH"}, {"name": "VK_SPACE"}, {"name": "VK_T"}, {"name": "VK_TAB"}, {"name": "VK_UP"}, {"name": "VK_V"}, {"name": "VK_X"}]}, {"name": "java.beans.PropertyVetoException"}, {"name": "java.io.FilePermission"}, {"name": "java.io.InputStream"}, {"name": "java.io.Reader"}, {"name": "java.lang.Double"}, {"name": "java.lang.Integer"}, {"name": "java.lang.Object", "queryAllPublicMethods": true, "methods": [{"name": "equals", "parameterTypes": ["java.lang.Object"]}, {"name": "hashCode", "parameterTypes": []}, {"name": "toString", "parameterTypes": []}]}, {"name": "java.lang.RuntimePermission"}, {"name": "java.lang.String", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String"]}]}, {"name": "java.lang.Thread", "methods": [{"name": "getContextClassLoader", "parameterTypes": []}]}, {"name": "java.lang.Throwable", "methods": [{"name": "addSuppressed", "parameterTypes": ["java.lang.Throwable"]}, {"name": "initCause", "parameterTypes": ["java.lang.Throwable"]}]}, {"name": "java.lang.invoke.MethodHandle", "methods": [{"name": "bindTo", "parameterTypes": ["java.lang.Object"]}, {"name": "invokeWithArguments", "parameterTypes": ["java.lang.Object[]"]}]}, {"name": "java.lang.invoke.MethodHandles", "methods": [{"name": "lookup", "parameterTypes": []}, {"name": "privateLookupIn", "parameterTypes": ["java.lang.Class", "java.lang.invoke.MethodHandles$Lookup"]}]}, {"name": "java.lang.invoke.MethodHandles$Lookup", "methods": [{"name": "findSpecial", "parameterTypes": ["java.lang.Class", "java.lang.String", "java.lang.invoke.MethodType", "java.lang.Class"]}, {"name": "in", "parameterTypes": ["java.lang.Class"]}, {"name": "unreflectSpecial", "parameterTypes": ["java.lang.reflect.Method", "java.lang.Class"]}]}, {"name": "java.lang.invoke.MethodType", "methods": [{"name": "methodType", "parameterTypes": ["java.lang.Class", "java.lang.Class[]"]}]}, {"name": "java.lang.reflect.Method", "methods": [{"name": "isDefault", "parameterTypes": []}, {"name": "isVarArgs", "parameterTypes": []}]}, {"name": "java.net.NetPermission"}, {"name": "java.net.SocketPermission"}, {"name": "java.net.URL"}, {"name": "java.net.URLPermission", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "java.lang.String"]}]}, {"name": "java.nio.Buffer"}, {"name": "java.nio.ByteBuffer"}, {"name": "java.nio.CharBuffer"}, {"name": "java.rmi.MarshalledObject", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Object"]}, {"name": "get", "parameterTypes": []}]}, {"name": "java.rmi.Remote"}, {"name": "java.security.AlgorithmParametersSpi"}, {"name": "java.security.AllPermission"}, {"name": "java.security.KeyStoreSpi"}, {"name": "java.security.SecureRandomParameters"}, {"name": "java.security.SecurityPermission"}, {"name": "java.security.interfaces.ECPrivateKey"}, {"name": "java.security.interfaces.ECPublicKey"}, {"name": "java.security.interfaces.RSAPrivateKey"}, {"name": "java.security.interfaces.RSAPublicKey"}, {"name": "java.util.Date"}, {"name": "java.util.LinkedList", "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "java.util.List"}, {"name": "java.util.PropertyPermission"}, {"name": "javax.security.auth.x500.X500Principal", "fields": [{"name": "thisX500Name"}], "methods": [{"name": "<init>", "parameterTypes": ["sun.security.x509.X500Name"]}]}, {"name": "javax.swing.plaf.basic.BasicFormattedTextFieldUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.basic.BasicListUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.basic.BasicMenuItemUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}, {"name": "loadActionMap", "parameterTypes": ["javax.swing.plaf.basic.LazyActionMap"]}]}, {"name": "javax.swing.plaf.basic.BasicMenuUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.basic.BasicOptionPaneUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}, {"name": "loadActionMap", "parameterTypes": ["javax.swing.plaf.basic.LazyActionMap"]}]}, {"name": "javax.swing.plaf.basic.BasicPanelUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.basic.BasicPopupMenuUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}, {"name": "loadActionMap", "parameterTypes": ["javax.swing.plaf.basic.LazyActionMap"]}]}, {"name": "javax.swing.plaf.basic.BasicSpinnerUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.basic.BasicTableHeaderUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.basic.BasicTableUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}, {"name": "loadActionMap", "parameterTypes": ["javax.swing.plaf.basic.LazyActionMap"]}]}, {"name": "javax.swing.plaf.basic.BasicTextAreaUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.basic.BasicToolBarSeparatorUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.basic.BasicViewportUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalButtonUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalCheckBoxUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalComboBoxUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalFileChooserUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalLabelUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalMenuBarUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalPopupMenuSeparatorUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalProgressBarUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalRootPaneUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalScrollBarUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalScrollPaneUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalTabbedPaneUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalTextFieldUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalToggleButtonUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalToolBarUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalToolTipUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "net.sf.jasperreports.components.ComponentsExtensionsRegistryFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.components.headertoolbar.HeaderToolbarConditionalStyleProviderExtensionFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.components.headertoolbar.HeaderToolbarParameterContributorExtensionFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.data.DataAdapterParameterContributorExtensionsRegistryFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.data.DefaultDataAdapterServiceExtensionsRegistryFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.JRBand"}, {"name": "net.sf.jasperreports.engine.JRExpression"}, {"name": "net.sf.jasperreports.engine.JRField"}, {"name": "net.sf.jasperreports.engine.JRParameter"}, {"name": "net.sf.jasperreports.engine.JRSortField"}, {"name": "net.sf.jasperreports.engine.JRStyle"}, {"name": "net.sf.jasperreports.engine.base.JRBaseElement", "queryAllPublicMethods": true}, {"name": "net.sf.jasperreports.engine.base.JRBaseField", "methods": [{"name": "setDescription", "parameterTypes": ["java.lang.String"]}]}, {"name": "net.sf.jasperreports.engine.design.JRDesignBand"}, {"name": "net.sf.jasperreports.engine.design.JRDesignConditionalStyle", "methods": [{"name": "setConditionExpression", "parameterTypes": ["net.sf.jasperreports.engine.JRExpression"]}]}, {"name": "net.sf.jasperreports.engine.design.JRDesignDataset", "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["net.sf.jasperreports.engine.design.JRDesignQuery"]}]}, {"name": "net.sf.jasperreports.engine.design.JRDesignElement", "queryAllPublicMethods": true, "methods": [{"name": "setPrintWhenExpression", "parameterTypes": ["net.sf.jasperreports.engine.JRExpression"]}]}, {"name": "net.sf.jasperreports.engine.design.JRDesignElementGroup", "methods": [{"name": "addElement", "parameterTypes": ["net.sf.jasperreports.engine.design.JRDesignElement"]}]}, {"name": "net.sf.jasperreports.engine.design.JRDesignExpression", "methods": [{"name": "setText", "parameterTypes": ["java.lang.String"]}]}, {"name": "net.sf.jasperreports.engine.design.JRDesignField"}, {"name": "net.sf.jasperreports.engine.design.JRDesignQuery", "methods": [{"name": "setText", "parameterTypes": ["java.lang.String"]}]}, {"name": "net.sf.jasperreports.engine.design.JRDesignSection", "methods": [{"name": "addBand", "parameterTypes": ["net.sf.jasperreports.engine.JRBand"]}]}, {"name": "net.sf.jasperreports.engine.design.JRDesignStaticText", "queryAllPublicMethods": true, "methods": [{"name": "setText", "parameterTypes": ["java.lang.String"]}]}, {"name": "net.sf.jasperreports.engine.design.JRDesignTextElement", "queryAllPublicMethods": true}, {"name": "net.sf.jasperreports.engine.design.JRDesignTextField", "methods": [{"name": "setExpression", "parameterTypes": ["net.sf.jasperreports.engine.JRExpression"]}]}, {"name": "net.sf.jasperreports.engine.design.JRJavacCompiler", "methods": [{"name": "<init>", "parameterTypes": ["net.sf.jasperreports.engine.JasperReportsContext"]}]}, {"name": "net.sf.jasperreports.engine.JasperReport", "methods": [{"name": "<init>", "parameterTypes": ["net.sf.jasperreports.engine.JRReport", "java.lang.String", "java.io.Serializable", "net.sf.jasperreports.engine.JRExpressionCollector", "java.lang.String"]}]}, {"name": "net.sf.jasperreports.engine.design.JRJdtCompiler"}, {"name": "net.sf.jasperreports.engine.design.JasperDesign", "methods": [{"name": "addDataset", "parameterTypes": ["net.sf.jasperreports.engine.design.JRDesignDataset"]}, {"name": "addField", "parameterTypes": ["net.sf.jasperreports.engine.JRField"]}, {"name": "addParameter", "parameterTypes": ["net.sf.jasperreports.engine.JRParameter"]}, {"name": "addSortField", "parameterTypes": ["net.sf.jasperreports.engine.JRSortField"]}, {"name": "addStyle", "parameterTypes": ["net.sf.jasperreports.engine.JRStyle"]}, {"name": "setColumnHeader", "parameterTypes": ["net.sf.jasperreports.engine.JRBand"]}, {"name": "setLast<PERSON>age<PERSON><PERSON><PERSON>", "parameterTypes": ["net.sf.jasperreports.engine.JRBand"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["net.sf.jasperreports.engine.design.JRDesignQuery"]}, {"name": "setTitle", "parameterTypes": ["net.sf.jasperreports.engine.JRBand"]}]}, {"name": "net.sf.jasperreports.engine.export.DefaultExporterFilterFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.export.ElementKeyExporterFilterFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.export.ElementPropertyExporterFilterFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.export.JROriginExporterFilterFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.export.MatcherExportFilterMappingExtensionsRegistryFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.export.MatcherExporterFilterFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.fill.TextMeasurerFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.fonts.SimpleFontExtensionsRegistryFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.query.JRJdbcQueryExecuterFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.query.SQLQueryClauseFunctionsExtensions", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.style.PropertyStyleProviderExtensionFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRBandFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRBoxFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRConditionalStyleFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRConditionalStyleFillerFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRDatasetFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRElementFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRExpressionFactory$BooleanExpressionFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRFieldFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRFontFactory$TextElementFontFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRLineFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRParameterFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRPenFactory$Bottom", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRPenFactory$Box", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRPenFactory$Left", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRPenFactory$Right", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRPenFactory$Top", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRPropertyFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRPropertyObject", "methods": [{"name": "setValue", "parameterTypes": ["java.lang.String"]}]}, {"name": "net.sf.jasperreports.engine.xml.JRQueryFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRReportSaxParserFactory", "methods": [{"name": "<init>", "parameterTypes": ["net.sf.jasperreports.engine.JasperReportsContext"]}]}, {"name": "net.sf.jasperreports.engine.xml.JRSectionFactory$DetailSectionFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRSortFieldFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRStaticTextFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRStringExpressionFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRStyleFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRTextElementFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRTextFieldFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.engine.xml.JRXmlLoader", "methods": [{"name": "setJasperDesign", "parameterTypes": ["net.sf.jasperreports.engine.design.JasperDesign"]}]}, {"name": "net.sf.jasperreports.engine.xml.JasperDesignFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.export.PdfExporterConfiguration", "methods": [{"name": "getAllowedPermissions", "parameterTypes": []}, {"name": "getDeniedPermissions", "parameterTypes": []}, {"name": "getMetadataAuthor", "parameterTypes": []}, {"name": "getMetadataCreator", "parameterTypes": []}, {"name": "getMetadataKeywords", "parameterTypes": []}, {"name": "getMetadataSubject", "parameterTypes": []}, {"name": "getMetadataTitle", "parameterTypes": []}, {"name": "getPdfJavaScript", "parameterTypes": []}, {"name": "getPdfVersion", "parameterTypes": []}, {"name": "getPdfaConformance", "parameterTypes": []}, {"name": "getPrintScaling", "parameterTypes": []}, {"name": "getTagLanguage", "parameterTypes": []}, {"name": "isCompressed", "parameterTypes": []}, {"name": "isCreatingBatchModeBookmarks", "parameterTypes": []}, {"name": "isEncrypted", "parameterTypes": []}, {"name": "isTagged", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.export.PdfReportConfiguration", "methods": [{"name": "getEvenPageOffsetX", "parameterTypes": []}, {"name": "getEvenPageOffsetY", "parameterTypes": []}, {"name": "getOddPageOffsetX", "parameterTypes": []}, {"name": "getOddPageOffsetY", "parameterTypes": []}, {"name": "isForceLineBreakPolicy", "parameterTypes": []}, {"name": "isSizePageToContent", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.export.ReportExportConfiguration", "methods": [{"name": "getEndPageIndex", "parameterTypes": []}, {"name": "getExporterFilter", "parameterTypes": []}, {"name": "getOffsetX", "parameterTypes": []}, {"name": "getOffsetY", "parameterTypes": []}, {"name": "getPageIndex", "parameterTypes": []}, {"name": "getProgressMonitor", "parameterTypes": []}, {"name": "getStartPageIndex", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.export.pdf.classic.ClassicPdfProducerFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.export.type.PdfPrintScalingEnum", "methods": [{"name": "getByName", "parameterTypes": ["java.lang.String"]}]}, {"name": "net.sf.jasperreports.export.type.PdfVersionEnum", "methods": [{"name": "getByName", "parameterTypes": ["java.lang.String"]}]}, {"name": "net.sf.jasperreports.export.type.PdfaConformanceEnum", "methods": [{"name": "getByName", "parameterTypes": ["java.lang.String"]}]}, {"name": "net.sf.jasperreports.extensions.DefaultExtensionsRegistry", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.extensions.DefaultExtensionsRegistryFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.governors.GovernorExtensionsRegistryFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.parts.PartComponentsExtensionsRegistryFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.repo.DefaultRepositoryExtensionsRegistryFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.types.date.DateRangeQueryClauseExtensions", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.util.JacksonMappingExtensionsRegistryFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.web.util.ContentTypeMappingExtensionsRegistryFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.sf.jasperreports.web.util.WebResourceHandlersExtensionRegistryFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.apache.commons.logging.LogFactory"}, {"name": "org.apache.commons.logging.impl.Jdk14Logger", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String"]}]}, {"name": "org.apache.commons.logging.impl.Log4JLogger"}, {"name": "org.apache.commons.logging.impl.LogFactoryImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.apache.commons.logging.impl.WeakHashtable", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.eclipse.jdt.internal.compiler.Compiler"}, {"name": "org.eclipse.jdt.internal.compiler.env.AccessRestriction"}, {"name": "org.eclipse.jdt.internal.compiler.env.NameEnvironmentAnswer", "methods": [{"name": "<init>", "parameterTypes": ["org.eclipse.jdt.internal.compiler.env.IBinaryType", "org.eclipse.jdt.internal.compiler.env.AccessRestriction"]}, {"name": "<init>", "parameterTypes": ["org.eclipse.jdt.internal.compiler.env.ICompilationUnit", "org.eclipse.jdt.internal.compiler.env.AccessRestriction"]}]}, {"name": "org.eclipse.jdt.internal.compiler.util.Messages", "allDeclaredFields": true}, {"name": "oshi.jna.Struct$CloseableSystemInfo", "allDeclaredFields": true}, {"name": "sun.awt.Symbol", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.awt.Win32FontManager", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.awt.shell.Win32ShellFolderManager2", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.awt.windows.WingDings", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.java2d.marlin.DMarlinRenderingEngine", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.pkcs12.PKCS12KeyStore", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.pkcs12.PKCS12KeyStore$DualFormatPKCS12", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.DRBG", "methods": [{"name": "<init>", "parameterTypes": ["java.security.SecureRandomParameters"]}]}, {"name": "sun.security.provider.DSA$SHA224withDSA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.DSA$SHA256withDSA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.JavaKeyStore$DualFormatJKS", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.JavaKeyStore$JKS", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.MD5", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.SHA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.SHA2$SHA224", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.SHA2$SHA256", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.SHA5$SHA384", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.SHA5$SHA512", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.X509Factory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.certpath.PKIXCertPathValidator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.rsa.PSSParameters", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.rsa.RSAKeyFactory$Legacy", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.rsa.RSAPSSSignature", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.rsa.RSASignature$SHA224withRSA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.rsa.RSASignature$SHA256withRSA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.ssl.KeyManagerFactoryImpl$SunX509", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.ssl.SSLContextImpl$DefaultSSLContext", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.ssl.TrustManagerFactoryImpl$PKIXFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.util.ObjectIdentifier"}, {"name": "sun.security.x509.AuthorityInfoAccessExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.AuthorityKeyIdentifierExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.BasicConstraintsExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.CRLDistributionPointsExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.CertificateExtensions"}, {"name": "sun.security.x509.CertificatePoliciesExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.ExtendedKeyUsageExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.IssuerAlternativeNameExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.KeyUsageExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.NetscapeCertTypeExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.PrivateKeyUsageExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.SubjectAlternativeNameExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.SubjectKeyIdentifierExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}]