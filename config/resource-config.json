{"resources": {"includes": [{"pattern": "\\QLineItem.jrxml\\E"}, {"pattern": "\\QMETA-INF/services/ch.qos.logback.classic.spi.Configurator\\E"}, {"pattern": "\\QMETA-INF/services/org.slf4j.spi.SLF4JServiceProvider\\E"}, {"pattern": "\\Qcom/lowagie/text/pdf/fonts/Helvetica.afm\\E"}, {"pattern": "\\Qcom/lowagie/text/pdf/fonts/glyphlist.txt\\E"}, {"pattern": "\\Qcom/lowagie/text/version.properties\\E"}, {"pattern": "\\Qcom/sun/jna/win32-x86-64/jnidispatch.dll\\E"}, {"pattern": "\\Qdefault.jasperreports.properties\\E"}, {"pattern": "\\Qimages/Refresh16.gif\\E"}, {"pattern": "\\Qimages/apply.png\\E"}, {"pattern": "\\Qimages/cancel.png\\E"}, {"pattern": "\\Qimages/exclude.jpg\\E"}, {"pattern": "\\Qimages/folder.png\\E"}, {"pattern": "\\Qimages/pa_icon.png\\E"}, {"pattern": "\\Qimages/pdf.png\\E"}, {"pattern": "\\Qimages/push.png\\E"}, {"pattern": "\\Qimages/reset.png\\E"}, {"pattern": "\\Qimages/search.png\\E"}, {"pattern": "\\Qimages/up-down-icon-16.jpg\\E"}, {"pattern": "\\Qjasperreports_extension.properties\\E"}, {"pattern": "\\Qjava/io/Serializable.class\\E"}, {"pattern": "\\Qjava/lang/Boolean.class\\E"}, {"pattern": "\\Qjava/lang/Cloneable.class\\E"}, {"pattern": "\\Qjava/lang/Comparable.class\\E"}, {"pattern": "\\Qjava/lang/Object.class\\E"}, {"pattern": "\\Qjava/lang/String.class\\E"}, {"pattern": "\\Qjava/lang/Throwable.class\\E"}, {"pattern": "\\Qjava/lang/constant/Constable.class\\E"}, {"pattern": "\\Qjava/util/Date.class\\E"}, {"pattern": "\\Qjava/util/Map.class\\E"}, {"pattern": "\\Qlogback.xml\\E"}, {"pattern": "\\Qnet/sf/jasperreports/components/components.xsd\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/JRException.class\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/JRExpression.class\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/dtds/jasperprint-dtd-compat.xsd\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/dtds/jasperprint.dtd\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/dtds/jasperprint.xsd\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/dtds/jasperreport-dtd-compat.xsd\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/dtds/jasperreport.dtd\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/dtds/jasperreport.xsd\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/dtds/jaspertemplate-dtd-compat.xsd\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/dtds/jaspertemplate.dtd\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/dtds/jaspertemplate.xsd\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/fill/DatasetExpressionEvaluator.class\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/fill/JREvaluator.class\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/fill/JRExpressionEvalException.class\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/fill/JRFillField.class\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/fill/JRFillParameter.class\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/fill/JRFillVariable.class\\E"}, {"pattern": "\\Qnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum.class\\E"}, {"pattern": "\\Qnet/sf/jasperreports/fonts/icons/icons.ttf\\E"}, {"pattern": "\\Qnet/sf/jasperreports/fonts/jasperreports-fonts.xml\\E"}, {"pattern": "\\Qnet/sf/jasperreports/parts/parts.xsd\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/messages.properties\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser1.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser10.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser11.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser12.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser13.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser14.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser15.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser16.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser17.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser18.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser19.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser2.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser20.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser21.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser22.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser23.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser24.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser3.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser4.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser5.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser6.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser7.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser8.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/parser9.rsc\\E"}, {"pattern": "\\Qorg/eclipse/jdt/internal/compiler/parser/readableNames.props\\E"}, {"pattern": "\\Qoshi.architecture.properties\\E"}, {"pattern": "\\Qoshi.properties\\E"}, {"pattern": "\\Qpa.properties\\E"}, {"pattern": "\\Qsun/datatransfer/resources/flavormap.properties\\E"}]}, "bundles": [{"name": "com.partskick.gui.resources.MessageResource", "classNames": ["com.partskick.gui.resources.MessageResource"]}, {"name": "com.sun.org.apache.xerces.internal.impl.xpath.regex.message", "locales": [""]}, {"name": "com.sun.swing.internal.plaf.basic.resources.basic", "classNames": ["com.sun.swing.internal.plaf.basic.resources.basic", "com.sun.swing.internal.plaf.basic.resources.basic_zh_CN"]}, {"name": "com.sun.swing.internal.plaf.metal.resources.metal", "classNames": ["com.sun.swing.internal.plaf.metal.resources.metal", "com.sun.swing.internal.plaf.metal.resources.metal_zh_CN"]}, {"name": "org.eclipse.jdt.internal.compiler.problem.messages", "locales": [""]}, {"name": "sun.awt.resources.awt", "classNames": ["sun.awt.resources.awt", "sun.awt.resources.awt_zh_CN"]}]}