[Setup]
AIVersion=9.3
AIEdition=1
GUID={F0F4EE54-B83C-4438-9819-6FAB4410C57C}
AppName=PartsKick
AppVersion=1.5.3
AppDescription=
CompanyName=
WebSite=
SupportLink=
InstallLevel=1
UpgradeMode=0
IfInstalled=0
RunAsAdmin=1
Windows 7=1
Windows 8=1
Windows 8.1=1
Windows 10=1
Windows 11=1
Windows Server 2008=1
Windows Server 2008 R2=1
Windows Server 2012=1
Windows Server 2012 R2=1
Windows Server 2016=1
Windows Server 2019=1
Windows Server 2022=1
SystemType=0
Internet=1
CloseApp=0
CloseAppFile=PartsKick.exe
CloseAppText=PartsKick
PackageType=0
SetupFolder=D:\Hunter\PK
SetupFileName=PartsKickSetup1.5.3
IconFile=Default
SourceDir=D:\Hunter\PK\partskick-ui\dist
InstallDir=C:\PartsKick
MainExe=
ProgramGroup=<AppName>
Uninstall=1
ShowAddRemove=1
ProductIcon=2
SilentUninstall=0
UninstallForce=1
UninstallRestart=0
UninstallSettings=0
UninstallSettingsCommand=
VisitUninstallPage=0
UninstallURL=http://www.website.com/uninstall.html
UninstallCloseMainExe=1
UninstallCloseApp=0
UninstallCloseAppFile=File.exe
UninstallCloseAppText=App
Updater=0
UpdateURL=http://www.website.com/update.txt
CheckUpdateBefore=0
CheckUpdaterTitle=0
CheckUseDownloader=0
AllowChangeUpdate=0
AutoCheckUpdate=0
UpdateFrequency=2
AutoSilentUpdate=0
CheckUpdateOnLaunch=0
CheckSmartNotification=0
UpdateSmartMethod=0
CheckUpdatePassParam=0
CheckUpdaterParam=?id=<AppVersion>
UpdaterParameters=
LaunchOnStatup=0
SelectFolderMode=1
AltInstallDir=<AppData>\<AppName>
DataExtractParam=-o"<InstallDir>" -aoa
UninstallFileName=Uninstall.exe
LangIDMethod=0
AllowInstallIfPrereqFailed=0
SetupParameters=
Theme=2
WizardBitmap=<AIDir>\Bitmaps\Wizard\01.bmp
HeaderBitmap=<AIDir>\Bitmaps\Header\01.bmp
LogoBitmap=D:\Hunter\PK\partskick-ui\dist_extra\pa.ico
DialogWelcome=1
DialogLicense=0
DialogReadme=0
DialogUserInfo=0
DialogDestinationFolder=1
DialogAdditionalTasks=1
DialogReady=0
DialogFinish=1
ShowPublisher=1
HideFileNames=1
Beep=1
License=
Readme=
AcceptAgreement=0
Languages=English,
DefaultLanguage=English
AutoDetectLanguage=0
HideOtherLanguages=0
QueryText=0
QueryTextName=
QueryTextReq=0
QueryTextHide=0
QueryCustom=0
QueryCustomName=
QueryCustomReq=0
QueryCustomHide=0
QueryCustomType=0
QueryCustomMustExists=0
QueryKey=0
QueryKeyName=Key:
QueryKeyReq=0
QueryKeyHide=0
QueryKeyHideReg=0
KeysCount=0
InfoCustomBox=0
InfoCustomBoxReq=0
InfoCustomBoxName=
CustomComboBox=0
CustomComboBoxName=
CustomComboBoxItems=
CustomComboDefItem=
ComboBoxDisableOnUpgrade=0
CustomComponents=0
CustomComponentsText=
CustomComponentsDesc=
CustomComponentsFull=0
CalcCompoSize=0
CalcReqSizeMethod=0
CompoMustSelect=0
RestartComputer=0
LaunchFile=0
LaunchFileName=
LaunchFileChecked=0
LaunchFileAdmin=0
FinishCurrentDir=0
CustomAction=0
CustomActionName=Visit Web Site
CustomActionCommand=<WebSite>
CustomActionChecked=0
CustomActionAdmin=0
TotalFiles=404
SourceDirFiles=1
SourceDirSize=0

[GUI]
FontName=Verdana
LogoMode=1
LogoOffset=0
LogoIconSize=2
Theme=0
LogoAlignClient=0
LogoAutoSize=1
LogoStretch=0
LogoProportional=0
LogoTransparent=0

[Files]
0=D:\Hunter\PK\partskick-ui\dist_extra\pa.ico?<InstallDir>\pa.ico?1?1
1=<AIDir>\Uninstall.exe?<InstallDir>\Uninstall.exe?1?1
2=D:\Hunter\PK\partskick-ui\dist_extra\pa.ico?<InstallDir>\ProductIcon.ico?1?1
3=D:\Hunter\devtools\customjre\bin\api-ms-win-core-console-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-console-l1-1-0.dll?1?1
4=D:\Hunter\devtools\customjre\bin\api-ms-win-core-console-l1-2-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-console-l1-2-0.dll?1?1
5=D:\Hunter\devtools\customjre\bin\api-ms-win-core-datetime-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-datetime-l1-1-0.dll?1?1
6=D:\Hunter\devtools\customjre\bin\api-ms-win-core-debug-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-debug-l1-1-0.dll?1?1
7=D:\Hunter\devtools\customjre\bin\api-ms-win-core-errorhandling-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-errorhandling-l1-1-0.dll?1?1
8=D:\Hunter\devtools\customjre\bin\api-ms-win-core-file-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-file-l1-1-0.dll?1?1
9=D:\Hunter\devtools\customjre\bin\api-ms-win-core-file-l1-2-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-file-l1-2-0.dll?1?1
10=D:\Hunter\devtools\customjre\bin\api-ms-win-core-file-l2-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-file-l2-1-0.dll?1?1
11=D:\Hunter\devtools\customjre\bin\api-ms-win-core-handle-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-handle-l1-1-0.dll?1?1
12=D:\Hunter\devtools\customjre\bin\api-ms-win-core-heap-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-heap-l1-1-0.dll?1?1
13=D:\Hunter\devtools\customjre\bin\api-ms-win-core-interlocked-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-interlocked-l1-1-0.dll?1?1
14=D:\Hunter\devtools\customjre\bin\api-ms-win-core-libraryloader-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-libraryloader-l1-1-0.dll?1?1
15=D:\Hunter\devtools\customjre\bin\api-ms-win-core-localization-l1-2-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-localization-l1-2-0.dll?1?1
16=D:\Hunter\devtools\customjre\bin\api-ms-win-core-memory-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-memory-l1-1-0.dll?1?1
17=D:\Hunter\devtools\customjre\bin\api-ms-win-core-namedpipe-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-namedpipe-l1-1-0.dll?1?1
18=D:\Hunter\devtools\customjre\bin\api-ms-win-core-processenvironment-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-processenvironment-l1-1-0.dll?1?1
19=D:\Hunter\devtools\customjre\bin\api-ms-win-core-processthreads-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-processthreads-l1-1-0.dll?1?1
20=D:\Hunter\devtools\customjre\bin\api-ms-win-core-processthreads-l1-1-1.dll?<InstallDir>\customjre\bin\api-ms-win-core-processthreads-l1-1-1.dll?1?1
21=D:\Hunter\devtools\customjre\bin\api-ms-win-core-profile-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-profile-l1-1-0.dll?1?1
22=D:\Hunter\devtools\customjre\bin\api-ms-win-core-rtlsupport-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-rtlsupport-l1-1-0.dll?1?1
23=D:\Hunter\devtools\customjre\bin\api-ms-win-core-string-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-string-l1-1-0.dll?1?1
24=D:\Hunter\devtools\customjre\bin\api-ms-win-core-synch-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-synch-l1-1-0.dll?1?1
25=D:\Hunter\devtools\customjre\bin\api-ms-win-core-synch-l1-2-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-synch-l1-2-0.dll?1?1
26=D:\Hunter\devtools\customjre\bin\api-ms-win-core-sysinfo-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-sysinfo-l1-1-0.dll?1?1
27=D:\Hunter\devtools\customjre\bin\api-ms-win-core-timezone-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-timezone-l1-1-0.dll?1?1
28=D:\Hunter\devtools\customjre\bin\api-ms-win-core-util-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-core-util-l1-1-0.dll?1?1
29=D:\Hunter\devtools\customjre\bin\api-ms-win-crt-conio-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-crt-conio-l1-1-0.dll?1?1
30=D:\Hunter\devtools\customjre\bin\api-ms-win-crt-convert-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-crt-convert-l1-1-0.dll?1?1
31=D:\Hunter\devtools\customjre\bin\api-ms-win-crt-environment-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-crt-environment-l1-1-0.dll?1?1
32=D:\Hunter\devtools\customjre\bin\api-ms-win-crt-filesystem-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-crt-filesystem-l1-1-0.dll?1?1
33=D:\Hunter\devtools\customjre\bin\api-ms-win-crt-heap-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-crt-heap-l1-1-0.dll?1?1
34=D:\Hunter\devtools\customjre\bin\api-ms-win-crt-locale-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-crt-locale-l1-1-0.dll?1?1
35=D:\Hunter\devtools\customjre\bin\api-ms-win-crt-math-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-crt-math-l1-1-0.dll?1?1
36=D:\Hunter\devtools\customjre\bin\api-ms-win-crt-multibyte-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-crt-multibyte-l1-1-0.dll?1?1
37=D:\Hunter\devtools\customjre\bin\api-ms-win-crt-private-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-crt-private-l1-1-0.dll?1?1
38=D:\Hunter\devtools\customjre\bin\api-ms-win-crt-process-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-crt-process-l1-1-0.dll?1?1
39=D:\Hunter\devtools\customjre\bin\api-ms-win-crt-runtime-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-crt-runtime-l1-1-0.dll?1?1
40=D:\Hunter\devtools\customjre\bin\api-ms-win-crt-stdio-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-crt-stdio-l1-1-0.dll?1?1
41=D:\Hunter\devtools\customjre\bin\api-ms-win-crt-string-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-crt-string-l1-1-0.dll?1?1
42=D:\Hunter\devtools\customjre\bin\api-ms-win-crt-time-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-crt-time-l1-1-0.dll?1?1
43=D:\Hunter\devtools\customjre\bin\api-ms-win-crt-utility-l1-1-0.dll?<InstallDir>\customjre\bin\api-ms-win-crt-utility-l1-1-0.dll?1?1
44=D:\Hunter\devtools\customjre\bin\attach.dll?<InstallDir>\customjre\bin\attach.dll?1?1
45=D:\Hunter\devtools\customjre\bin\awt.dll?<InstallDir>\customjre\bin\awt.dll?1?1
46=D:\Hunter\devtools\customjre\bin\dt_shmem.dll?<InstallDir>\customjre\bin\dt_shmem.dll?1?1
47=D:\Hunter\devtools\customjre\bin\dt_socket.dll?<InstallDir>\customjre\bin\dt_socket.dll?1?1
48=D:\Hunter\devtools\customjre\bin\fontmanager.dll?<InstallDir>\customjre\bin\fontmanager.dll?1?1
49=D:\Hunter\devtools\customjre\bin\freetype.dll?<InstallDir>\customjre\bin\freetype.dll?1?1
50=D:\Hunter\devtools\customjre\bin\instrument.dll?<InstallDir>\customjre\bin\instrument.dll?1?1
51=D:\Hunter\devtools\customjre\bin\j2gss.dll?<InstallDir>\customjre\bin\j2gss.dll?1?1
52=D:\Hunter\devtools\customjre\bin\j2pcsc.dll?<InstallDir>\customjre\bin\j2pcsc.dll?1?1
53=D:\Hunter\devtools\customjre\bin\j2pkcs11.dll?<InstallDir>\customjre\bin\j2pkcs11.dll?1?1
54=D:\Hunter\devtools\customjre\bin\jaas.dll?<InstallDir>\customjre\bin\jaas.dll?1?1
55=D:\Hunter\devtools\customjre\bin\jabswitch.exe?<InstallDir>\customjre\bin\jabswitch.exe?1?1
56=D:\Hunter\devtools\customjre\bin\jaccessinspector.exe?<InstallDir>\customjre\bin\jaccessinspector.exe?1?1
57=D:\Hunter\devtools\customjre\bin\jaccesswalker.exe?<InstallDir>\customjre\bin\jaccesswalker.exe?1?1
58=D:\Hunter\devtools\customjre\bin\jar.exe?<InstallDir>\customjre\bin\jar.exe?1?1
59=D:\Hunter\devtools\customjre\bin\jarsigner.exe?<InstallDir>\customjre\bin\jarsigner.exe?1?1
60=D:\Hunter\devtools\customjre\bin\java.dll?<InstallDir>\customjre\bin\java.dll?1?1
61=D:\Hunter\devtools\customjre\bin\java.exe?<InstallDir>\customjre\bin\java.exe?1?1
62=D:\Hunter\devtools\customjre\bin\javaaccessbridge.dll?<InstallDir>\customjre\bin\javaaccessbridge.dll?1?1
63=D:\Hunter\devtools\customjre\bin\javac.exe?<InstallDir>\customjre\bin\javac.exe?1?1
64=D:\Hunter\devtools\customjre\bin\javadoc.exe?<InstallDir>\customjre\bin\javadoc.exe?1?1
65=D:\Hunter\devtools\customjre\bin\javajpeg.dll?<InstallDir>\customjre\bin\javajpeg.dll?1?1
66=D:\Hunter\devtools\customjre\bin\javap.exe?<InstallDir>\customjre\bin\javap.exe?1?1
67=D:\Hunter\devtools\customjre\bin\javaw.exe?<InstallDir>\customjre\bin\javaw.exe?1?1
68=D:\Hunter\devtools\customjre\bin\jawt.dll?<InstallDir>\customjre\bin\jawt.dll?1?1
69=D:\Hunter\devtools\customjre\bin\jcmd.exe?<InstallDir>\customjre\bin\jcmd.exe?1?1
70=D:\Hunter\devtools\customjre\bin\jconsole.exe?<InstallDir>\customjre\bin\jconsole.exe?1?1
71=D:\Hunter\devtools\customjre\bin\jdb.exe?<InstallDir>\customjre\bin\jdb.exe?1?1
72=D:\Hunter\devtools\customjre\bin\jdeprscan.exe?<InstallDir>\customjre\bin\jdeprscan.exe?1?1
73=D:\Hunter\devtools\customjre\bin\jdeps.exe?<InstallDir>\customjre\bin\jdeps.exe?1?1
74=D:\Hunter\devtools\customjre\bin\jdwp.dll?<InstallDir>\customjre\bin\jdwp.dll?1?1
75=D:\Hunter\devtools\customjre\bin\jfr.exe?<InstallDir>\customjre\bin\jfr.exe?1?1
76=D:\Hunter\devtools\customjre\bin\jhsdb.exe?<InstallDir>\customjre\bin\jhsdb.exe?1?1
77=D:\Hunter\devtools\customjre\bin\jimage.dll?<InstallDir>\customjre\bin\jimage.dll?1?1
78=D:\Hunter\devtools\customjre\bin\jimage.exe?<InstallDir>\customjre\bin\jimage.exe?1?1
79=D:\Hunter\devtools\customjre\bin\jinfo.exe?<InstallDir>\customjre\bin\jinfo.exe?1?1
80=D:\Hunter\devtools\customjre\bin\jli.dll?<InstallDir>\customjre\bin\jli.dll?1?1
81=D:\Hunter\devtools\customjre\bin\jlink.exe?<InstallDir>\customjre\bin\jlink.exe?1?1
82=D:\Hunter\devtools\customjre\bin\jmap.exe?<InstallDir>\customjre\bin\jmap.exe?1?1
83=D:\Hunter\devtools\customjre\bin\jmod.exe?<InstallDir>\customjre\bin\jmod.exe?1?1
84=D:\Hunter\devtools\customjre\bin\jpackage.dll?<InstallDir>\customjre\bin\jpackage.dll?1?1
85=D:\Hunter\devtools\customjre\bin\jpackage.exe?<InstallDir>\customjre\bin\jpackage.exe?1?1
86=D:\Hunter\devtools\customjre\bin\jps.exe?<InstallDir>\customjre\bin\jps.exe?1?1
87=D:\Hunter\devtools\customjre\bin\jrunscript.exe?<InstallDir>\customjre\bin\jrunscript.exe?1?1
88=D:\Hunter\devtools\customjre\bin\jshell.exe?<InstallDir>\customjre\bin\jshell.exe?1?1
89=D:\Hunter\devtools\customjre\bin\jsound.dll?<InstallDir>\customjre\bin\jsound.dll?1?1
90=D:\Hunter\devtools\customjre\bin\jstack.exe?<InstallDir>\customjre\bin\jstack.exe?1?1
91=D:\Hunter\devtools\customjre\bin\jstat.exe?<InstallDir>\customjre\bin\jstat.exe?1?1
92=D:\Hunter\devtools\customjre\bin\jstatd.exe?<InstallDir>\customjre\bin\jstatd.exe?1?1
93=D:\Hunter\devtools\customjre\bin\jsvml.dll?<InstallDir>\customjre\bin\jsvml.dll?1?1
94=D:\Hunter\devtools\customjre\bin\keytool.exe?<InstallDir>\customjre\bin\keytool.exe?1?1
95=D:\Hunter\devtools\customjre\bin\kinit.exe?<InstallDir>\customjre\bin\kinit.exe?1?1
96=D:\Hunter\devtools\customjre\bin\klist.exe?<InstallDir>\customjre\bin\klist.exe?1?1
97=D:\Hunter\devtools\customjre\bin\ktab.exe?<InstallDir>\customjre\bin\ktab.exe?1?1
98=D:\Hunter\devtools\customjre\bin\lcms.dll?<InstallDir>\customjre\bin\lcms.dll?1?1
99=D:\Hunter\devtools\customjre\bin\le.dll?<InstallDir>\customjre\bin\le.dll?1?1
100=D:\Hunter\devtools\customjre\bin\management.dll?<InstallDir>\customjre\bin\management.dll?1?1
101=D:\Hunter\devtools\customjre\bin\management_agent.dll?<InstallDir>\customjre\bin\management_agent.dll?1?1
102=D:\Hunter\devtools\customjre\bin\management_ext.dll?<InstallDir>\customjre\bin\management_ext.dll?1?1
103=D:\Hunter\devtools\customjre\bin\mlib_image.dll?<InstallDir>\customjre\bin\mlib_image.dll?1?1
104=D:\Hunter\devtools\customjre\bin\msvcp140.dll?<InstallDir>\customjre\bin\msvcp140.dll?1?1
105=D:\Hunter\devtools\customjre\bin\net.dll?<InstallDir>\customjre\bin\net.dll?1?1
106=D:\Hunter\devtools\customjre\bin\nio.dll?<InstallDir>\customjre\bin\nio.dll?1?1
107=D:\Hunter\devtools\customjre\bin\prefs.dll?<InstallDir>\customjre\bin\prefs.dll?1?1
108=D:\Hunter\devtools\customjre\bin\rmi.dll?<InstallDir>\customjre\bin\rmi.dll?1?1
109=D:\Hunter\devtools\customjre\bin\rmiregistry.exe?<InstallDir>\customjre\bin\rmiregistry.exe?1?1
110=D:\Hunter\devtools\customjre\bin\saproc.dll?<InstallDir>\customjre\bin\saproc.dll?1?1
111=D:\Hunter\devtools\customjre\bin\serialver.exe?<InstallDir>\customjre\bin\serialver.exe?1?1
112=D:\Hunter\devtools\customjre\bin\server\jvm.dll?<InstallDir>\customjre\bin\server\jvm.dll?1?1
113=D:\Hunter\devtools\customjre\bin\splashscreen.dll?<InstallDir>\customjre\bin\splashscreen.dll?1?1
114=D:\Hunter\devtools\customjre\bin\sspi_bridge.dll?<InstallDir>\customjre\bin\sspi_bridge.dll?1?1
115=D:\Hunter\devtools\customjre\bin\sunmscapi.dll?<InstallDir>\customjre\bin\sunmscapi.dll?1?1
116=D:\Hunter\devtools\customjre\bin\ucrtbase.dll?<InstallDir>\customjre\bin\ucrtbase.dll?1?1
117=D:\Hunter\devtools\customjre\bin\vcruntime140.dll?<InstallDir>\customjre\bin\vcruntime140.dll?1?1
118=D:\Hunter\devtools\customjre\bin\vcruntime140_1.dll?<InstallDir>\customjre\bin\vcruntime140_1.dll?1?1
119=D:\Hunter\devtools\customjre\bin\verify.dll?<InstallDir>\customjre\bin\verify.dll?1?1
120=D:\Hunter\devtools\customjre\bin\w2k_lsa_auth.dll?<InstallDir>\customjre\bin\w2k_lsa_auth.dll?1?1
121=D:\Hunter\devtools\customjre\bin\windowsaccessbridge-64.dll?<InstallDir>\customjre\bin\windowsaccessbridge-64.dll?1?1
122=D:\Hunter\devtools\customjre\bin\WinFallbackLookup.dll?<InstallDir>\customjre\bin\WinFallbackLookup.dll?1?1
123=D:\Hunter\devtools\customjre\bin\zip.dll?<InstallDir>\customjre\bin\zip.dll?1?1
124=D:\Hunter\devtools\customjre\conf\logging.properties?<InstallDir>\customjre\conf\logging.properties?1?1
125=D:\Hunter\devtools\customjre\conf\management\jmxremote.access?<InstallDir>\customjre\conf\management\jmxremote.access?1?1
126=D:\Hunter\devtools\customjre\conf\management\jmxremote.password.template?<InstallDir>\customjre\conf\management\jmxremote.password.template?1?1
127=D:\Hunter\devtools\customjre\conf\management\management.properties?<InstallDir>\customjre\conf\management\management.properties?1?1
128=D:\Hunter\devtools\customjre\conf\net.properties?<InstallDir>\customjre\conf\net.properties?1?1
129=D:\Hunter\devtools\customjre\conf\security\java.policy?<InstallDir>\customjre\conf\security\java.policy?1?1
130=D:\Hunter\devtools\customjre\conf\security\java.security?<InstallDir>\customjre\conf\security\java.security?1?1
131=D:\Hunter\devtools\customjre\conf\security\policy\limited\default_local.policy?<InstallDir>\customjre\conf\security\policy\limited\default_local.policy?1?1
132=D:\Hunter\devtools\customjre\conf\security\policy\limited\default_US_export.policy?<InstallDir>\customjre\conf\security\policy\limited\default_US_export.policy?1?1
133=D:\Hunter\devtools\customjre\conf\security\policy\limited\exempt_local.policy?<InstallDir>\customjre\conf\security\policy\limited\exempt_local.policy?1?1
134=D:\Hunter\devtools\customjre\conf\security\policy\README.txt?<InstallDir>\customjre\conf\security\policy\README.txt?1?1
135=D:\Hunter\devtools\customjre\conf\security\policy\unlimited\default_local.policy?<InstallDir>\customjre\conf\security\policy\unlimited\default_local.policy?1?1
136=D:\Hunter\devtools\customjre\conf\security\policy\unlimited\default_US_export.policy?<InstallDir>\customjre\conf\security\policy\unlimited\default_US_export.policy?1?1
137=D:\Hunter\devtools\customjre\conf\sound.properties?<InstallDir>\customjre\conf\sound.properties?1?1
138=D:\Hunter\devtools\customjre\legal\java.base\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.base\ADDITIONAL_LICENSE_INFO?1?1
139=D:\Hunter\devtools\customjre\legal\java.base\aes.md?<InstallDir>\customjre\legal\java.base\aes.md?1?1
140=D:\Hunter\devtools\customjre\legal\java.base\asm.md?<InstallDir>\customjre\legal\java.base\asm.md?1?1
141=D:\Hunter\devtools\customjre\legal\java.base\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.base\ASSEMBLY_EXCEPTION?1?1
142=D:\Hunter\devtools\customjre\legal\java.base\c-libutl.md?<InstallDir>\customjre\legal\java.base\c-libutl.md?1?1
143=D:\Hunter\devtools\customjre\legal\java.base\cldr.md?<InstallDir>\customjre\legal\java.base\cldr.md?1?1
144=D:\Hunter\devtools\customjre\legal\java.base\icu.md?<InstallDir>\customjre\legal\java.base\icu.md?1?1
145=D:\Hunter\devtools\customjre\legal\java.base\LICENSE?<InstallDir>\customjre\legal\java.base\LICENSE?1?1
146=D:\Hunter\devtools\customjre\legal\java.base\public_suffix.md?<InstallDir>\customjre\legal\java.base\public_suffix.md?1?1
147=D:\Hunter\devtools\customjre\legal\java.base\unicode.md?<InstallDir>\customjre\legal\java.base\unicode.md?1?1
148=D:\Hunter\devtools\customjre\legal\java.base\wepoll.md?<InstallDir>\customjre\legal\java.base\wepoll.md?1?1
149=D:\Hunter\devtools\customjre\legal\java.base\zlib.md?<InstallDir>\customjre\legal\java.base\zlib.md?1?1
150=D:\Hunter\devtools\customjre\legal\java.compiler\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.compiler\ADDITIONAL_LICENSE_INFO?1?1
151=D:\Hunter\devtools\customjre\legal\java.compiler\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.compiler\ASSEMBLY_EXCEPTION?1?1
152=D:\Hunter\devtools\customjre\legal\java.compiler\LICENSE?<InstallDir>\customjre\legal\java.compiler\LICENSE?1?1
153=D:\Hunter\devtools\customjre\legal\java.datatransfer\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.datatransfer\ADDITIONAL_LICENSE_INFO?1?1
154=D:\Hunter\devtools\customjre\legal\java.datatransfer\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.datatransfer\ASSEMBLY_EXCEPTION?1?1
155=D:\Hunter\devtools\customjre\legal\java.datatransfer\LICENSE?<InstallDir>\customjre\legal\java.datatransfer\LICENSE?1?1
156=D:\Hunter\devtools\customjre\legal\java.desktop\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.desktop\ADDITIONAL_LICENSE_INFO?1?1
157=D:\Hunter\devtools\customjre\legal\java.desktop\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.desktop\ASSEMBLY_EXCEPTION?1?1
158=D:\Hunter\devtools\customjre\legal\java.desktop\colorimaging.md?<InstallDir>\customjre\legal\java.desktop\colorimaging.md?1?1
159=D:\Hunter\devtools\customjre\legal\java.desktop\freetype.md?<InstallDir>\customjre\legal\java.desktop\freetype.md?1?1
160=D:\Hunter\devtools\customjre\legal\java.desktop\giflib.md?<InstallDir>\customjre\legal\java.desktop\giflib.md?1?1
161=D:\Hunter\devtools\customjre\legal\java.desktop\harfbuzz.md?<InstallDir>\customjre\legal\java.desktop\harfbuzz.md?1?1
162=D:\Hunter\devtools\customjre\legal\java.desktop\jpeg.md?<InstallDir>\customjre\legal\java.desktop\jpeg.md?1?1
163=D:\Hunter\devtools\customjre\legal\java.desktop\lcms.md?<InstallDir>\customjre\legal\java.desktop\lcms.md?1?1
164=D:\Hunter\devtools\customjre\legal\java.desktop\libpng.md?<InstallDir>\customjre\legal\java.desktop\libpng.md?1?1
165=D:\Hunter\devtools\customjre\legal\java.desktop\LICENSE?<InstallDir>\customjre\legal\java.desktop\LICENSE?1?1
166=D:\Hunter\devtools\customjre\legal\java.desktop\mesa3d.md?<InstallDir>\customjre\legal\java.desktop\mesa3d.md?1?1
167=D:\Hunter\devtools\customjre\legal\java.instrument\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.instrument\ADDITIONAL_LICENSE_INFO?1?1
168=D:\Hunter\devtools\customjre\legal\java.instrument\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.instrument\ASSEMBLY_EXCEPTION?1?1
169=D:\Hunter\devtools\customjre\legal\java.instrument\LICENSE?<InstallDir>\customjre\legal\java.instrument\LICENSE?1?1
170=D:\Hunter\devtools\customjre\legal\java.logging\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.logging\ADDITIONAL_LICENSE_INFO?1?1
171=D:\Hunter\devtools\customjre\legal\java.logging\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.logging\ASSEMBLY_EXCEPTION?1?1
172=D:\Hunter\devtools\customjre\legal\java.logging\LICENSE?<InstallDir>\customjre\legal\java.logging\LICENSE?1?1
173=D:\Hunter\devtools\customjre\legal\java.management\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.management\ADDITIONAL_LICENSE_INFO?1?1
174=D:\Hunter\devtools\customjre\legal\java.management\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.management\ASSEMBLY_EXCEPTION?1?1
175=D:\Hunter\devtools\customjre\legal\java.management\LICENSE?<InstallDir>\customjre\legal\java.management\LICENSE?1?1
176=D:\Hunter\devtools\customjre\legal\java.management.rmi\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.management.rmi\ADDITIONAL_LICENSE_INFO?1?1
177=D:\Hunter\devtools\customjre\legal\java.management.rmi\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.management.rmi\ASSEMBLY_EXCEPTION?1?1
178=D:\Hunter\devtools\customjre\legal\java.management.rmi\LICENSE?<InstallDir>\customjre\legal\java.management.rmi\LICENSE?1?1
179=D:\Hunter\devtools\customjre\legal\java.naming\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.naming\ADDITIONAL_LICENSE_INFO?1?1
180=D:\Hunter\devtools\customjre\legal\java.naming\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.naming\ASSEMBLY_EXCEPTION?1?1
181=D:\Hunter\devtools\customjre\legal\java.naming\LICENSE?<InstallDir>\customjre\legal\java.naming\LICENSE?1?1
182=D:\Hunter\devtools\customjre\legal\java.net.http\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.net.http\ADDITIONAL_LICENSE_INFO?1?1
183=D:\Hunter\devtools\customjre\legal\java.net.http\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.net.http\ASSEMBLY_EXCEPTION?1?1
184=D:\Hunter\devtools\customjre\legal\java.net.http\LICENSE?<InstallDir>\customjre\legal\java.net.http\LICENSE?1?1
185=D:\Hunter\devtools\customjre\legal\java.prefs\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.prefs\ADDITIONAL_LICENSE_INFO?1?1
186=D:\Hunter\devtools\customjre\legal\java.prefs\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.prefs\ASSEMBLY_EXCEPTION?1?1
187=D:\Hunter\devtools\customjre\legal\java.prefs\LICENSE?<InstallDir>\customjre\legal\java.prefs\LICENSE?1?1
188=D:\Hunter\devtools\customjre\legal\java.rmi\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.rmi\ADDITIONAL_LICENSE_INFO?1?1
189=D:\Hunter\devtools\customjre\legal\java.rmi\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.rmi\ASSEMBLY_EXCEPTION?1?1
190=D:\Hunter\devtools\customjre\legal\java.rmi\LICENSE?<InstallDir>\customjre\legal\java.rmi\LICENSE?1?1
191=D:\Hunter\devtools\customjre\legal\java.scripting\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.scripting\ADDITIONAL_LICENSE_INFO?1?1
192=D:\Hunter\devtools\customjre\legal\java.scripting\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.scripting\ASSEMBLY_EXCEPTION?1?1
193=D:\Hunter\devtools\customjre\legal\java.scripting\LICENSE?<InstallDir>\customjre\legal\java.scripting\LICENSE?1?1
194=D:\Hunter\devtools\customjre\legal\java.se\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.se\ADDITIONAL_LICENSE_INFO?1?1
195=D:\Hunter\devtools\customjre\legal\java.se\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.se\ASSEMBLY_EXCEPTION?1?1
196=D:\Hunter\devtools\customjre\legal\java.se\LICENSE?<InstallDir>\customjre\legal\java.se\LICENSE?1?1
197=D:\Hunter\devtools\customjre\legal\java.security.jgss\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.security.jgss\ADDITIONAL_LICENSE_INFO?1?1
198=D:\Hunter\devtools\customjre\legal\java.security.jgss\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.security.jgss\ASSEMBLY_EXCEPTION?1?1
199=D:\Hunter\devtools\customjre\legal\java.security.jgss\LICENSE?<InstallDir>\customjre\legal\java.security.jgss\LICENSE?1?1
200=D:\Hunter\devtools\customjre\legal\java.security.sasl\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.security.sasl\ADDITIONAL_LICENSE_INFO?1?1
201=D:\Hunter\devtools\customjre\legal\java.security.sasl\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.security.sasl\ASSEMBLY_EXCEPTION?1?1
202=D:\Hunter\devtools\customjre\legal\java.security.sasl\LICENSE?<InstallDir>\customjre\legal\java.security.sasl\LICENSE?1?1
203=D:\Hunter\devtools\customjre\legal\java.smartcardio\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.smartcardio\ADDITIONAL_LICENSE_INFO?1?1
204=D:\Hunter\devtools\customjre\legal\java.smartcardio\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.smartcardio\ASSEMBLY_EXCEPTION?1?1
205=D:\Hunter\devtools\customjre\legal\java.smartcardio\LICENSE?<InstallDir>\customjre\legal\java.smartcardio\LICENSE?1?1
206=D:\Hunter\devtools\customjre\legal\java.sql\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.sql\ADDITIONAL_LICENSE_INFO?1?1
207=D:\Hunter\devtools\customjre\legal\java.sql\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.sql\ASSEMBLY_EXCEPTION?1?1
208=D:\Hunter\devtools\customjre\legal\java.sql\LICENSE?<InstallDir>\customjre\legal\java.sql\LICENSE?1?1
209=D:\Hunter\devtools\customjre\legal\java.sql.rowset\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.sql.rowset\ADDITIONAL_LICENSE_INFO?1?1
210=D:\Hunter\devtools\customjre\legal\java.sql.rowset\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.sql.rowset\ASSEMBLY_EXCEPTION?1?1
211=D:\Hunter\devtools\customjre\legal\java.sql.rowset\LICENSE?<InstallDir>\customjre\legal\java.sql.rowset\LICENSE?1?1
212=D:\Hunter\devtools\customjre\legal\java.transaction.xa\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.transaction.xa\ADDITIONAL_LICENSE_INFO?1?1
213=D:\Hunter\devtools\customjre\legal\java.transaction.xa\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.transaction.xa\ASSEMBLY_EXCEPTION?1?1
214=D:\Hunter\devtools\customjre\legal\java.transaction.xa\LICENSE?<InstallDir>\customjre\legal\java.transaction.xa\LICENSE?1?1
215=D:\Hunter\devtools\customjre\legal\java.xml\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.xml\ADDITIONAL_LICENSE_INFO?1?1
216=D:\Hunter\devtools\customjre\legal\java.xml\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.xml\ASSEMBLY_EXCEPTION?1?1
217=D:\Hunter\devtools\customjre\legal\java.xml\bcel.md?<InstallDir>\customjre\legal\java.xml\bcel.md?1?1
218=D:\Hunter\devtools\customjre\legal\java.xml\dom.md?<InstallDir>\customjre\legal\java.xml\dom.md?1?1
219=D:\Hunter\devtools\customjre\legal\java.xml\jcup.md?<InstallDir>\customjre\legal\java.xml\jcup.md?1?1
220=D:\Hunter\devtools\customjre\legal\java.xml\LICENSE?<InstallDir>\customjre\legal\java.xml\LICENSE?1?1
221=D:\Hunter\devtools\customjre\legal\java.xml\xalan.md?<InstallDir>\customjre\legal\java.xml\xalan.md?1?1
222=D:\Hunter\devtools\customjre\legal\java.xml\xerces.md?<InstallDir>\customjre\legal\java.xml\xerces.md?1?1
223=D:\Hunter\devtools\customjre\legal\java.xml.crypto\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\java.xml.crypto\ADDITIONAL_LICENSE_INFO?1?1
224=D:\Hunter\devtools\customjre\legal\java.xml.crypto\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\java.xml.crypto\ASSEMBLY_EXCEPTION?1?1
225=D:\Hunter\devtools\customjre\legal\java.xml.crypto\LICENSE?<InstallDir>\customjre\legal\java.xml.crypto\LICENSE?1?1
226=D:\Hunter\devtools\customjre\legal\java.xml.crypto\santuario.md?<InstallDir>\customjre\legal\java.xml.crypto\santuario.md?1?1
227=D:\Hunter\devtools\customjre\legal\jdk.accessibility\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.accessibility\ADDITIONAL_LICENSE_INFO?1?1
228=D:\Hunter\devtools\customjre\legal\jdk.accessibility\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.accessibility\ASSEMBLY_EXCEPTION?1?1
229=D:\Hunter\devtools\customjre\legal\jdk.accessibility\LICENSE?<InstallDir>\customjre\legal\jdk.accessibility\LICENSE?1?1
230=D:\Hunter\devtools\customjre\legal\jdk.attach\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.attach\ADDITIONAL_LICENSE_INFO?1?1
231=D:\Hunter\devtools\customjre\legal\jdk.attach\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.attach\ASSEMBLY_EXCEPTION?1?1
232=D:\Hunter\devtools\customjre\legal\jdk.attach\LICENSE?<InstallDir>\customjre\legal\jdk.attach\LICENSE?1?1
233=D:\Hunter\devtools\customjre\legal\jdk.charsets\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.charsets\ADDITIONAL_LICENSE_INFO?1?1
234=D:\Hunter\devtools\customjre\legal\jdk.charsets\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.charsets\ASSEMBLY_EXCEPTION?1?1
235=D:\Hunter\devtools\customjre\legal\jdk.charsets\LICENSE?<InstallDir>\customjre\legal\jdk.charsets\LICENSE?1?1
236=D:\Hunter\devtools\customjre\legal\jdk.compiler\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.compiler\ADDITIONAL_LICENSE_INFO?1?1
237=D:\Hunter\devtools\customjre\legal\jdk.compiler\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.compiler\ASSEMBLY_EXCEPTION?1?1
238=D:\Hunter\devtools\customjre\legal\jdk.compiler\LICENSE?<InstallDir>\customjre\legal\jdk.compiler\LICENSE?1?1
239=D:\Hunter\devtools\customjre\legal\jdk.crypto.cryptoki\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.crypto.cryptoki\ADDITIONAL_LICENSE_INFO?1?1
240=D:\Hunter\devtools\customjre\legal\jdk.crypto.cryptoki\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.crypto.cryptoki\ASSEMBLY_EXCEPTION?1?1
241=D:\Hunter\devtools\customjre\legal\jdk.crypto.cryptoki\LICENSE?<InstallDir>\customjre\legal\jdk.crypto.cryptoki\LICENSE?1?1
242=D:\Hunter\devtools\customjre\legal\jdk.crypto.cryptoki\pkcs11cryptotoken.md?<InstallDir>\customjre\legal\jdk.crypto.cryptoki\pkcs11cryptotoken.md?1?1
243=D:\Hunter\devtools\customjre\legal\jdk.crypto.cryptoki\pkcs11wrapper.md?<InstallDir>\customjre\legal\jdk.crypto.cryptoki\pkcs11wrapper.md?1?1
244=D:\Hunter\devtools\customjre\legal\jdk.crypto.ec\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.crypto.ec\ADDITIONAL_LICENSE_INFO?1?1
245=D:\Hunter\devtools\customjre\legal\jdk.crypto.ec\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.crypto.ec\ASSEMBLY_EXCEPTION?1?1
246=D:\Hunter\devtools\customjre\legal\jdk.crypto.ec\LICENSE?<InstallDir>\customjre\legal\jdk.crypto.ec\LICENSE?1?1
247=D:\Hunter\devtools\customjre\legal\jdk.crypto.mscapi\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.crypto.mscapi\ADDITIONAL_LICENSE_INFO?1?1
248=D:\Hunter\devtools\customjre\legal\jdk.crypto.mscapi\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.crypto.mscapi\ASSEMBLY_EXCEPTION?1?1
249=D:\Hunter\devtools\customjre\legal\jdk.crypto.mscapi\LICENSE?<InstallDir>\customjre\legal\jdk.crypto.mscapi\LICENSE?1?1
250=D:\Hunter\devtools\customjre\legal\jdk.dynalink\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.dynalink\ADDITIONAL_LICENSE_INFO?1?1
251=D:\Hunter\devtools\customjre\legal\jdk.dynalink\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.dynalink\ASSEMBLY_EXCEPTION?1?1
252=D:\Hunter\devtools\customjre\legal\jdk.dynalink\dynalink.md?<InstallDir>\customjre\legal\jdk.dynalink\dynalink.md?1?1
253=D:\Hunter\devtools\customjre\legal\jdk.dynalink\LICENSE?<InstallDir>\customjre\legal\jdk.dynalink\LICENSE?1?1
254=D:\Hunter\devtools\customjre\legal\jdk.editpad\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.editpad\ADDITIONAL_LICENSE_INFO?1?1
255=D:\Hunter\devtools\customjre\legal\jdk.editpad\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.editpad\ASSEMBLY_EXCEPTION?1?1
256=D:\Hunter\devtools\customjre\legal\jdk.editpad\LICENSE?<InstallDir>\customjre\legal\jdk.editpad\LICENSE?1?1
257=D:\Hunter\devtools\customjre\legal\jdk.hotspot.agent\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.hotspot.agent\ADDITIONAL_LICENSE_INFO?1?1
258=D:\Hunter\devtools\customjre\legal\jdk.hotspot.agent\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.hotspot.agent\ASSEMBLY_EXCEPTION?1?1
259=D:\Hunter\devtools\customjre\legal\jdk.hotspot.agent\LICENSE?<InstallDir>\customjre\legal\jdk.hotspot.agent\LICENSE?1?1
260=D:\Hunter\devtools\customjre\legal\jdk.httpserver\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.httpserver\ADDITIONAL_LICENSE_INFO?1?1
261=D:\Hunter\devtools\customjre\legal\jdk.httpserver\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.httpserver\ASSEMBLY_EXCEPTION?1?1
262=D:\Hunter\devtools\customjre\legal\jdk.httpserver\LICENSE?<InstallDir>\customjre\legal\jdk.httpserver\LICENSE?1?1
263=D:\Hunter\devtools\customjre\legal\jdk.incubator.foreign\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.incubator.foreign\ADDITIONAL_LICENSE_INFO?1?1
264=D:\Hunter\devtools\customjre\legal\jdk.incubator.foreign\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.incubator.foreign\ASSEMBLY_EXCEPTION?1?1
265=D:\Hunter\devtools\customjre\legal\jdk.incubator.foreign\LICENSE?<InstallDir>\customjre\legal\jdk.incubator.foreign\LICENSE?1?1
266=D:\Hunter\devtools\customjre\legal\jdk.incubator.vector\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.incubator.vector\ADDITIONAL_LICENSE_INFO?1?1
267=D:\Hunter\devtools\customjre\legal\jdk.incubator.vector\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.incubator.vector\ASSEMBLY_EXCEPTION?1?1
268=D:\Hunter\devtools\customjre\legal\jdk.incubator.vector\LICENSE?<InstallDir>\customjre\legal\jdk.incubator.vector\LICENSE?1?1
269=D:\Hunter\devtools\customjre\legal\jdk.internal.ed\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.internal.ed\ADDITIONAL_LICENSE_INFO?1?1
270=D:\Hunter\devtools\customjre\legal\jdk.internal.ed\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.internal.ed\ASSEMBLY_EXCEPTION?1?1
271=D:\Hunter\devtools\customjre\legal\jdk.internal.ed\LICENSE?<InstallDir>\customjre\legal\jdk.internal.ed\LICENSE?1?1
272=D:\Hunter\devtools\customjre\legal\jdk.internal.jvmstat\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.internal.jvmstat\ADDITIONAL_LICENSE_INFO?1?1
273=D:\Hunter\devtools\customjre\legal\jdk.internal.jvmstat\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.internal.jvmstat\ASSEMBLY_EXCEPTION?1?1
274=D:\Hunter\devtools\customjre\legal\jdk.internal.jvmstat\LICENSE?<InstallDir>\customjre\legal\jdk.internal.jvmstat\LICENSE?1?1
275=D:\Hunter\devtools\customjre\legal\jdk.internal.le\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.internal.le\ADDITIONAL_LICENSE_INFO?1?1
276=D:\Hunter\devtools\customjre\legal\jdk.internal.le\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.internal.le\ASSEMBLY_EXCEPTION?1?1
277=D:\Hunter\devtools\customjre\legal\jdk.internal.le\jline.md?<InstallDir>\customjre\legal\jdk.internal.le\jline.md?1?1
278=D:\Hunter\devtools\customjre\legal\jdk.internal.le\LICENSE?<InstallDir>\customjre\legal\jdk.internal.le\LICENSE?1?1
279=D:\Hunter\devtools\customjre\legal\jdk.internal.opt\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.internal.opt\ADDITIONAL_LICENSE_INFO?1?1
280=D:\Hunter\devtools\customjre\legal\jdk.internal.opt\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.internal.opt\ASSEMBLY_EXCEPTION?1?1
281=D:\Hunter\devtools\customjre\legal\jdk.internal.opt\jopt-simple.md?<InstallDir>\customjre\legal\jdk.internal.opt\jopt-simple.md?1?1
282=D:\Hunter\devtools\customjre\legal\jdk.internal.opt\LICENSE?<InstallDir>\customjre\legal\jdk.internal.opt\LICENSE?1?1
283=D:\Hunter\devtools\customjre\legal\jdk.internal.vm.ci\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.internal.vm.ci\ADDITIONAL_LICENSE_INFO?1?1
284=D:\Hunter\devtools\customjre\legal\jdk.internal.vm.ci\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.internal.vm.ci\ASSEMBLY_EXCEPTION?1?1
285=D:\Hunter\devtools\customjre\legal\jdk.internal.vm.ci\LICENSE?<InstallDir>\customjre\legal\jdk.internal.vm.ci\LICENSE?1?1
286=D:\Hunter\devtools\customjre\legal\jdk.internal.vm.compiler\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.internal.vm.compiler\ADDITIONAL_LICENSE_INFO?1?1
287=D:\Hunter\devtools\customjre\legal\jdk.internal.vm.compiler\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.internal.vm.compiler\ASSEMBLY_EXCEPTION?1?1
288=D:\Hunter\devtools\customjre\legal\jdk.internal.vm.compiler\LICENSE?<InstallDir>\customjre\legal\jdk.internal.vm.compiler\LICENSE?1?1
289=D:\Hunter\devtools\customjre\legal\jdk.internal.vm.compiler.management\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.internal.vm.compiler.management\ADDITIONAL_LICENSE_INFO?1?1
290=D:\Hunter\devtools\customjre\legal\jdk.internal.vm.compiler.management\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.internal.vm.compiler.management\ASSEMBLY_EXCEPTION?1?1
291=D:\Hunter\devtools\customjre\legal\jdk.internal.vm.compiler.management\LICENSE?<InstallDir>\customjre\legal\jdk.internal.vm.compiler.management\LICENSE?1?1
292=D:\Hunter\devtools\customjre\legal\jdk.jartool\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.jartool\ADDITIONAL_LICENSE_INFO?1?1
293=D:\Hunter\devtools\customjre\legal\jdk.jartool\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.jartool\ASSEMBLY_EXCEPTION?1?1
294=D:\Hunter\devtools\customjre\legal\jdk.jartool\LICENSE?<InstallDir>\customjre\legal\jdk.jartool\LICENSE?1?1
295=D:\Hunter\devtools\customjre\legal\jdk.javadoc\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.javadoc\ADDITIONAL_LICENSE_INFO?1?1
296=D:\Hunter\devtools\customjre\legal\jdk.javadoc\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.javadoc\ASSEMBLY_EXCEPTION?1?1
297=D:\Hunter\devtools\customjre\legal\jdk.javadoc\jquery.md?<InstallDir>\customjre\legal\jdk.javadoc\jquery.md?1?1
298=D:\Hunter\devtools\customjre\legal\jdk.javadoc\jqueryUI.md?<InstallDir>\customjre\legal\jdk.javadoc\jqueryUI.md?1?1
299=D:\Hunter\devtools\customjre\legal\jdk.javadoc\LICENSE?<InstallDir>\customjre\legal\jdk.javadoc\LICENSE?1?1
300=D:\Hunter\devtools\customjre\legal\jdk.jcmd\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.jcmd\ADDITIONAL_LICENSE_INFO?1?1
301=D:\Hunter\devtools\customjre\legal\jdk.jcmd\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.jcmd\ASSEMBLY_EXCEPTION?1?1
302=D:\Hunter\devtools\customjre\legal\jdk.jcmd\LICENSE?<InstallDir>\customjre\legal\jdk.jcmd\LICENSE?1?1
303=D:\Hunter\devtools\customjre\legal\jdk.jconsole\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.jconsole\ADDITIONAL_LICENSE_INFO?1?1
304=D:\Hunter\devtools\customjre\legal\jdk.jconsole\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.jconsole\ASSEMBLY_EXCEPTION?1?1
305=D:\Hunter\devtools\customjre\legal\jdk.jconsole\LICENSE?<InstallDir>\customjre\legal\jdk.jconsole\LICENSE?1?1
306=D:\Hunter\devtools\customjre\legal\jdk.jdeps\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.jdeps\ADDITIONAL_LICENSE_INFO?1?1
307=D:\Hunter\devtools\customjre\legal\jdk.jdeps\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.jdeps\ASSEMBLY_EXCEPTION?1?1
308=D:\Hunter\devtools\customjre\legal\jdk.jdeps\LICENSE?<InstallDir>\customjre\legal\jdk.jdeps\LICENSE?1?1
309=D:\Hunter\devtools\customjre\legal\jdk.jdi\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.jdi\ADDITIONAL_LICENSE_INFO?1?1
310=D:\Hunter\devtools\customjre\legal\jdk.jdi\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.jdi\ASSEMBLY_EXCEPTION?1?1
311=D:\Hunter\devtools\customjre\legal\jdk.jdi\LICENSE?<InstallDir>\customjre\legal\jdk.jdi\LICENSE?1?1
312=D:\Hunter\devtools\customjre\legal\jdk.jdwp.agent\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.jdwp.agent\ADDITIONAL_LICENSE_INFO?1?1
313=D:\Hunter\devtools\customjre\legal\jdk.jdwp.agent\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.jdwp.agent\ASSEMBLY_EXCEPTION?1?1
314=D:\Hunter\devtools\customjre\legal\jdk.jdwp.agent\LICENSE?<InstallDir>\customjre\legal\jdk.jdwp.agent\LICENSE?1?1
315=D:\Hunter\devtools\customjre\legal\jdk.jfr\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.jfr\ADDITIONAL_LICENSE_INFO?1?1
316=D:\Hunter\devtools\customjre\legal\jdk.jfr\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.jfr\ASSEMBLY_EXCEPTION?1?1
317=D:\Hunter\devtools\customjre\legal\jdk.jfr\LICENSE?<InstallDir>\customjre\legal\jdk.jfr\LICENSE?1?1
318=D:\Hunter\devtools\customjre\legal\jdk.jlink\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.jlink\ADDITIONAL_LICENSE_INFO?1?1
319=D:\Hunter\devtools\customjre\legal\jdk.jlink\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.jlink\ASSEMBLY_EXCEPTION?1?1
320=D:\Hunter\devtools\customjre\legal\jdk.jlink\LICENSE?<InstallDir>\customjre\legal\jdk.jlink\LICENSE?1?1
321=D:\Hunter\devtools\customjre\legal\jdk.jpackage\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.jpackage\ADDITIONAL_LICENSE_INFO?1?1
322=D:\Hunter\devtools\customjre\legal\jdk.jpackage\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.jpackage\ASSEMBLY_EXCEPTION?1?1
323=D:\Hunter\devtools\customjre\legal\jdk.jpackage\LICENSE?<InstallDir>\customjre\legal\jdk.jpackage\LICENSE?1?1
324=D:\Hunter\devtools\customjre\legal\jdk.jshell\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.jshell\ADDITIONAL_LICENSE_INFO?1?1
325=D:\Hunter\devtools\customjre\legal\jdk.jshell\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.jshell\ASSEMBLY_EXCEPTION?1?1
326=D:\Hunter\devtools\customjre\legal\jdk.jshell\LICENSE?<InstallDir>\customjre\legal\jdk.jshell\LICENSE?1?1
327=D:\Hunter\devtools\customjre\legal\jdk.jsobject\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.jsobject\ADDITIONAL_LICENSE_INFO?1?1
328=D:\Hunter\devtools\customjre\legal\jdk.jsobject\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.jsobject\ASSEMBLY_EXCEPTION?1?1
329=D:\Hunter\devtools\customjre\legal\jdk.jsobject\LICENSE?<InstallDir>\customjre\legal\jdk.jsobject\LICENSE?1?1
330=D:\Hunter\devtools\customjre\legal\jdk.jstatd\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.jstatd\ADDITIONAL_LICENSE_INFO?1?1
331=D:\Hunter\devtools\customjre\legal\jdk.jstatd\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.jstatd\ASSEMBLY_EXCEPTION?1?1
332=D:\Hunter\devtools\customjre\legal\jdk.jstatd\LICENSE?<InstallDir>\customjre\legal\jdk.jstatd\LICENSE?1?1
333=D:\Hunter\devtools\customjre\legal\jdk.localedata\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.localedata\ADDITIONAL_LICENSE_INFO?1?1
334=D:\Hunter\devtools\customjre\legal\jdk.localedata\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.localedata\ASSEMBLY_EXCEPTION?1?1
335=D:\Hunter\devtools\customjre\legal\jdk.localedata\cldr.md?<InstallDir>\customjre\legal\jdk.localedata\cldr.md?1?1
336=D:\Hunter\devtools\customjre\legal\jdk.localedata\LICENSE?<InstallDir>\customjre\legal\jdk.localedata\LICENSE?1?1
337=D:\Hunter\devtools\customjre\legal\jdk.localedata\thaidict.md?<InstallDir>\customjre\legal\jdk.localedata\thaidict.md?1?1
338=D:\Hunter\devtools\customjre\legal\jdk.management\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.management\ADDITIONAL_LICENSE_INFO?1?1
339=D:\Hunter\devtools\customjre\legal\jdk.management\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.management\ASSEMBLY_EXCEPTION?1?1
340=D:\Hunter\devtools\customjre\legal\jdk.management\LICENSE?<InstallDir>\customjre\legal\jdk.management\LICENSE?1?1
341=D:\Hunter\devtools\customjre\legal\jdk.management.agent\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.management.agent\ADDITIONAL_LICENSE_INFO?1?1
342=D:\Hunter\devtools\customjre\legal\jdk.management.agent\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.management.agent\ASSEMBLY_EXCEPTION?1?1
343=D:\Hunter\devtools\customjre\legal\jdk.management.agent\LICENSE?<InstallDir>\customjre\legal\jdk.management.agent\LICENSE?1?1
344=D:\Hunter\devtools\customjre\legal\jdk.management.jfr\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.management.jfr\ADDITIONAL_LICENSE_INFO?1?1
345=D:\Hunter\devtools\customjre\legal\jdk.management.jfr\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.management.jfr\ASSEMBLY_EXCEPTION?1?1
346=D:\Hunter\devtools\customjre\legal\jdk.management.jfr\LICENSE?<InstallDir>\customjre\legal\jdk.management.jfr\LICENSE?1?1
347=D:\Hunter\devtools\customjre\legal\jdk.naming.dns\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.naming.dns\ADDITIONAL_LICENSE_INFO?1?1
348=D:\Hunter\devtools\customjre\legal\jdk.naming.dns\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.naming.dns\ASSEMBLY_EXCEPTION?1?1
349=D:\Hunter\devtools\customjre\legal\jdk.naming.dns\LICENSE?<InstallDir>\customjre\legal\jdk.naming.dns\LICENSE?1?1
350=D:\Hunter\devtools\customjre\legal\jdk.naming.rmi\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.naming.rmi\ADDITIONAL_LICENSE_INFO?1?1
351=D:\Hunter\devtools\customjre\legal\jdk.naming.rmi\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.naming.rmi\ASSEMBLY_EXCEPTION?1?1
352=D:\Hunter\devtools\customjre\legal\jdk.naming.rmi\LICENSE?<InstallDir>\customjre\legal\jdk.naming.rmi\LICENSE?1?1
353=D:\Hunter\devtools\customjre\legal\jdk.net\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.net\ADDITIONAL_LICENSE_INFO?1?1
354=D:\Hunter\devtools\customjre\legal\jdk.net\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.net\ASSEMBLY_EXCEPTION?1?1
355=D:\Hunter\devtools\customjre\legal\jdk.net\LICENSE?<InstallDir>\customjre\legal\jdk.net\LICENSE?1?1
356=D:\Hunter\devtools\customjre\legal\jdk.nio.mapmode\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.nio.mapmode\ADDITIONAL_LICENSE_INFO?1?1
357=D:\Hunter\devtools\customjre\legal\jdk.nio.mapmode\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.nio.mapmode\ASSEMBLY_EXCEPTION?1?1
358=D:\Hunter\devtools\customjre\legal\jdk.nio.mapmode\LICENSE?<InstallDir>\customjre\legal\jdk.nio.mapmode\LICENSE?1?1
359=D:\Hunter\devtools\customjre\legal\jdk.random\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.random\ADDITIONAL_LICENSE_INFO?1?1
360=D:\Hunter\devtools\customjre\legal\jdk.random\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.random\ASSEMBLY_EXCEPTION?1?1
361=D:\Hunter\devtools\customjre\legal\jdk.random\LICENSE?<InstallDir>\customjre\legal\jdk.random\LICENSE?1?1
362=D:\Hunter\devtools\customjre\legal\jdk.sctp\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.sctp\ADDITIONAL_LICENSE_INFO?1?1
363=D:\Hunter\devtools\customjre\legal\jdk.sctp\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.sctp\ASSEMBLY_EXCEPTION?1?1
364=D:\Hunter\devtools\customjre\legal\jdk.sctp\LICENSE?<InstallDir>\customjre\legal\jdk.sctp\LICENSE?1?1
365=D:\Hunter\devtools\customjre\legal\jdk.security.auth\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.security.auth\ADDITIONAL_LICENSE_INFO?1?1
366=D:\Hunter\devtools\customjre\legal\jdk.security.auth\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.security.auth\ASSEMBLY_EXCEPTION?1?1
367=D:\Hunter\devtools\customjre\legal\jdk.security.auth\LICENSE?<InstallDir>\customjre\legal\jdk.security.auth\LICENSE?1?1
368=D:\Hunter\devtools\customjre\legal\jdk.security.jgss\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.security.jgss\ADDITIONAL_LICENSE_INFO?1?1
369=D:\Hunter\devtools\customjre\legal\jdk.security.jgss\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.security.jgss\ASSEMBLY_EXCEPTION?1?1
370=D:\Hunter\devtools\customjre\legal\jdk.security.jgss\LICENSE?<InstallDir>\customjre\legal\jdk.security.jgss\LICENSE?1?1
371=D:\Hunter\devtools\customjre\legal\jdk.unsupported\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.unsupported\ADDITIONAL_LICENSE_INFO?1?1
372=D:\Hunter\devtools\customjre\legal\jdk.unsupported\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.unsupported\ASSEMBLY_EXCEPTION?1?1
373=D:\Hunter\devtools\customjre\legal\jdk.unsupported\LICENSE?<InstallDir>\customjre\legal\jdk.unsupported\LICENSE?1?1
374=D:\Hunter\devtools\customjre\legal\jdk.unsupported.desktop\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.unsupported.desktop\ADDITIONAL_LICENSE_INFO?1?1
375=D:\Hunter\devtools\customjre\legal\jdk.unsupported.desktop\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.unsupported.desktop\ASSEMBLY_EXCEPTION?1?1
376=D:\Hunter\devtools\customjre\legal\jdk.unsupported.desktop\LICENSE?<InstallDir>\customjre\legal\jdk.unsupported.desktop\LICENSE?1?1
377=D:\Hunter\devtools\customjre\legal\jdk.xml.dom\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.xml.dom\ADDITIONAL_LICENSE_INFO?1?1
378=D:\Hunter\devtools\customjre\legal\jdk.xml.dom\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.xml.dom\ASSEMBLY_EXCEPTION?1?1
379=D:\Hunter\devtools\customjre\legal\jdk.xml.dom\LICENSE?<InstallDir>\customjre\legal\jdk.xml.dom\LICENSE?1?1
380=D:\Hunter\devtools\customjre\legal\jdk.zipfs\ADDITIONAL_LICENSE_INFO?<InstallDir>\customjre\legal\jdk.zipfs\ADDITIONAL_LICENSE_INFO?1?1
381=D:\Hunter\devtools\customjre\legal\jdk.zipfs\ASSEMBLY_EXCEPTION?<InstallDir>\customjre\legal\jdk.zipfs\ASSEMBLY_EXCEPTION?1?1
382=D:\Hunter\devtools\customjre\legal\jdk.zipfs\LICENSE?<InstallDir>\customjre\legal\jdk.zipfs\LICENSE?1?1
383=D:\Hunter\devtools\customjre\lib\classlist?<InstallDir>\customjre\lib\classlist?1?1
384=D:\Hunter\devtools\customjre\lib\ct.sym?<InstallDir>\customjre\lib\ct.sym?1?1
385=D:\Hunter\devtools\customjre\lib\fontconfig.bfc?<InstallDir>\customjre\lib\fontconfig.bfc?1?1
386=D:\Hunter\devtools\customjre\lib\fontconfig.properties.src?<InstallDir>\customjre\lib\fontconfig.properties.src?1?1
387=D:\Hunter\devtools\customjre\lib\jawt.lib?<InstallDir>\customjre\lib\jawt.lib?1?1
388=D:\Hunter\devtools\customjre\lib\jfr\default.jfc?<InstallDir>\customjre\lib\jfr\default.jfc?1?1
389=D:\Hunter\devtools\customjre\lib\jfr\profile.jfc?<InstallDir>\customjre\lib\jfr\profile.jfc?1?1
390=D:\Hunter\devtools\customjre\lib\jrt-fs.jar?<InstallDir>\customjre\lib\jrt-fs.jar?1?1
391=D:\Hunter\devtools\customjre\lib\jvm.cfg?<InstallDir>\customjre\lib\jvm.cfg?1?1
392=D:\Hunter\devtools\customjre\lib\jvm.lib?<InstallDir>\customjre\lib\jvm.lib?1?1
393=D:\Hunter\devtools\customjre\lib\modules?<InstallDir>\customjre\lib\modules?1?1
394=D:\Hunter\devtools\customjre\lib\psfont.properties.ja?<InstallDir>\customjre\lib\psfont.properties.ja?1?1
395=D:\Hunter\devtools\customjre\lib\psfontj2d.properties?<InstallDir>\customjre\lib\psfontj2d.properties?1?1
396=D:\Hunter\devtools\customjre\lib\security\blocked.certs?<InstallDir>\customjre\lib\security\blocked.certs?1?1
397=D:\Hunter\devtools\customjre\lib\security\cacerts?<InstallDir>\customjre\lib\security\cacerts?1?1
398=D:\Hunter\devtools\customjre\lib\security\default.policy?<InstallDir>\customjre\lib\security\default.policy?1?1
399=D:\Hunter\devtools\customjre\lib\security\public_suffix_list.dat?<InstallDir>\customjre\lib\security\public_suffix_list.dat?1?1
400=D:\Hunter\devtools\customjre\lib\tzdb.dat?<InstallDir>\customjre\lib\tzdb.dat?1?1
401=D:\Hunter\devtools\customjre\lib\tzmappings?<InstallDir>\customjre\lib\tzmappings?1?1
402=D:\Hunter\devtools\customjre\release?<InstallDir>\customjre\release?1?1
403=D:\Hunter\PK\partskick-ui\target\parts-kick-1.5.3.jar?<InstallDir>\parts-kick-1.5.3.jar?1?1

[Shortcuts]
0=<ProgramsMenu>*?PartsKick*?C:\PartsKick\customjre\bin\javaw.exe*?*?-jar parts-kick-1.5.3.jar*?<InstallDir>*?<InstallDir>\pa.ico*?0*?Minimized*?No
1=<Desktop>*?PartsKick*?C:\PartsKick\customjre\bin\javaw.exe*?*?-jar parts-kick-1.5.3.jar*?<InstallDir>*?<InstallDir>\pa.ico*?0*?Minimized*?No

