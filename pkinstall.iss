; Script generated by the Inno Setup Script Wizard.
; SEE THE DOCUMENTATION FOR DETAILS ON CREATING INNO SETUP SCRIPT FILES!

#define MyAppName "PartsKick"
#define MyAppVersion "2.3.0"
#define MyAppPublisher "1000471812 Ontario Inc"

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
; (To generate a new GUID, click Tools | Generate GUID inside the IDE.)
SignTool=SignTool $f
AppId={{C4DA4241-3BCE-4DB3-A9D9-42AD3D6628E6}}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
;AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
DefaultDirName=c:\{#MyAppName}
DisableProgramGroupPage=yes
PrivilegesRequired=lowest
OutputDir=..\install
OutputBaseFilename=pkupgrade
SetupIconFile=dist\pa.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[CustomMessages]
english.InstallFor=Install For
english.CurrentUser=Current User (Recommended)
english.AllUsers=All Users (Requires Administrator)
english.AdminRequired=Administrator Privilege

[Code]
var
  InstallTypePage: TInputOptionWizardPage;
  IsAllUsers: Boolean;
  IsUpgrade: Boolean;
  ResultCode: Integer;

function IsAdminInstallMode: Boolean; forward;
function GetRegistryParam: String; forward;

procedure WriteToLogFile(Message: String);
var
  LogFile: String;
  LogString: String;
begin
  LogFile := ExpandConstant('{tmp}\partskick_installer.log');
  LogString := GetDateTimeString('yyyy-mm-dd hh:nn:ss', '-', ':') + ' - ' + Message;
  SaveStringToFile(LogFile, LogString + #13#10, True);
end;

function GetRegistryParam: String;
begin
  if IsAdminInstallMode() then
    Result := 'registry-local-machine=true'
  else
    Result := 'registry-local-machine=false';
end;

function InitializeSetup(): Boolean;
var
  LogFile: String;
begin
  // Create a custom log file in a known location
  LogFile := ExpandConstant('{tmp}\partskick_installer.log');
  WriteToLogFile('=== PartsKick Installer Debug Log ===');
  WriteToLogFile('Starting installation process...');
  WriteToLogFile('Current time: ' + GetDateTimeString('yyyy-mm-dd hh:nn:ss', '-', ':'));
  WriteToLogFile('Log file location: ' + LogFile);
  
  // Also try to create a simple test file to verify file writing works
  try
    SaveStringToFile(ExpandConstant('{tmp}\test_log.txt'), 'Test log file created at ' + GetDateTimeString('yyyy-mm-dd hh:nn:ss', '-', ':') + #13#10, False);
    WriteToLogFile('Test log file created successfully');
  except
    WriteToLogFile('Failed to create test log file');
  end;
  
  // Check if this is an upgrade by looking for existing installation
  IsUpgrade := RegValueExists(HKLM, 'Software\Microsoft\Windows\CurrentVersion\Uninstall\{#SetupSetting("AppId")}_is1', 'DisplayName') or
               RegValueExists(HKCU, 'Software\Microsoft\Windows\CurrentVersion\Uninstall\{#SetupSetting("AppId")}_is1', 'DisplayName');
  
  // Alternative: Check for existing PartsKick installation
  if not IsUpgrade then
  begin
    IsUpgrade := RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\partskickui') or RegKeyExists(HKCU, 'Software\JavaSoft\Prefs\partskickui');
  end;
  
  // Log the detection result
  if IsUpgrade then
    Log('Detected existing PartsKick installation - this is an UPGRADE')
  else
    Log('No existing PartsKick installation found - this is a NEW INSTALLATION');
  
  Result := True;
end;



procedure InitializeWizard;
begin
  // select installation type
  InstallTypePage := CreateInputOptionPage(
    wpWelcome,
    'Select Installation Type',
    'Please Select',
    'Choose installation type:',
    True, False
  );
  
  InstallTypePage.Add(CustomMessage('CurrentUser'));
  InstallTypePage.Add(CustomMessage('AllUsers'));
  
  // default based on admin status
  if IsAdmin then
  begin
    // If running as admin, default to "All Users" since we have the privileges
    InstallTypePage.Values[1] := True;
  end
  else
  begin
    // If not running as admin, default to "Current User" to avoid privilege issues
    InstallTypePage.Values[0] := True;
  end;
end;

function NextButtonClick(CurPageID: Integer): Boolean;
begin
  Result := True;
  
  if CurPageID = InstallTypePage.ID then
  begin
    IsAllUsers := InstallTypePage.Values[1];
    if IsAllUsers then
      Log('User selected installation type - IsAllUsers: True')
    else
      Log('User selected installation type - IsAllUsers: False');
    if IsAdmin then
      Log('Current user is admin: True')
    else
      Log('Current user is admin: False');
    if InstallTypePage.Values[0] then
      Log('InstallTypePage.Values[0] (Current User): True')
    else
      Log('InstallTypePage.Values[0] (Current User): False');
    if InstallTypePage.Values[1] then
      Log('InstallTypePage.Values[1] (All Users): True')
    else
      Log('InstallTypePage.Values[1] (All Users): False');
    
    // If user selected "All Users" but we're not running as admin,
    // we need to restart with elevated privileges
    if IsAllUsers and not IsAdmin then
    begin
      if MsgBox('Installing for All Users requires administrator privileges. Would you like to restart the installer with administrator privileges?' + #13#10 + #13#10 + 'Click Yes to restart as administrator, or No to go back and select "Current User".',
        mbConfirmation, MB_YESNO) = IDYES then
      begin
        // restart setup for admin privilege
        ShellExec('runas', ExpandConstant('{srcexe}'), '', '', SW_SHOW, ewNoWait, ResultCode);
        Result := False;
        WizardForm.Close;
      end
      else
      begin
        // User cancelled, go back to the page
        Result := False;
      end;
    end;
  end;
end;

function IsAdminInstallMode: Boolean;
begin
  // During uninstall, detect installation type by checking registry keys
  // If HKLM keys exist, it was an "All Users" installation
  if RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\partskickui') or 
     RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\partskickui\db') or 
     RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\partskickui\reg') then
  begin
    Result := True; // All Users installation
    Log('IsAdminInstallMode: Detected All Users installation (HKLM JavaSoft Prefs keys exist)');
  end
  else
  begin
    Result := IsAllUsers; // Use the variable during installation
    if IsAllUsers then
      Log('IsAdminInstallMode: Using IsAllUsers variable: True')
    else
      Log('IsAdminInstallMode: Using IsAllUsers variable: False');
  end;
  if Result then
    Log('IsAdminInstallMode returning: True')
  else
    Log('IsAdminInstallMode returning: False');
end;

function InitializeUninstall(): Boolean;
begin
  Result := True;
  
  // Check if this is an upgrade by looking for existing installation
  IsUpgrade := RegValueExists(HKLM, 'Software\Microsoft\Windows\CurrentVersion\Uninstall\{#SetupSetting("AppId")}_is1', 'DisplayName') or
               RegValueExists(HKCU, 'Software\Microsoft\Windows\CurrentVersion\Uninstall\{#SetupSetting("AppId")}_is1', 'DisplayName');
  
  // Alternative: Check for existing PartsKick installation
  if not IsUpgrade then
  begin
    IsUpgrade := RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\partskickui') or RegKeyExists(HKCU, 'Software\JavaSoft\Prefs\partskickui');
  end;
  
  // Log the detection result
  if IsUpgrade then
    Log('Detected existing PartsKick installation - this is an UPGRADE during uninstall')
  else
    Log('No existing PartsKick installation found - this is a COMPLETE UNINSTALL');
  
  // Always check for admin privileges if HKLM keys exist, regardless of upgrade status
  Log('Checking for HKLM registry keys that require admin privileges...');
  if RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\partskickui') then
    Log('HKLM\Software\JavaSoft\Prefs\partskickui exists: True')
  else
    Log('HKLM\Software\JavaSoft\Prefs\partskickui exists: False');
  if RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\partskickui\db') then
    Log('HKLM\Software\JavaSoft\Prefs\partskickui\db exists: True')
  else
    Log('HKLM\Software\JavaSoft\Prefs\partskickui\db exists: False');
  if RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\partskickui\reg') then
    Log('HKLM\Software\JavaSoft\Prefs\partskickui\reg exists: True')
  else
    Log('HKLM\Software\JavaSoft\Prefs\partskickui\reg exists: False');
  if IsAdmin then
    Log('Current user is admin: True')
  else
    Log('Current user is admin: False');
  
  if (RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\partskickui') or 
      RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\partskickui\db') or 
      RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\partskickui\reg')) and not IsAdmin then
  begin
    if MsgBox('Request for administrator priviledge?', 
              mbConfirmation, MB_YESNO) = IDYES then
    begin
      ShellExec('runas', ExpandConstant('{uninstallexe}'), '', '', SW_SHOW, ewNoWait, ResultCode);
      Result := False;
    end
    else
    begin
      MsgBox('Uninstall cancelled', mbInformation, MB_OK);
      Result := False;
    end;
  end;
end;

procedure CurUninstallStepChanged(CurUninstallStep: TUninstallStep);
begin
  if CurUninstallStep = usUninstall then
  begin
    Log('Starting uninstall cleanup process...');
    
    // Always remove PartsKick-specific registry keys
    if RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\partskickui') then
    begin
      Log('Removing HKLM\Software\JavaSoft\Prefs\partskickui registry key');
      RegDeleteKeyIncludingSubkeys(HKLM, 'Software\JavaSoft\Prefs\partskickui');
    end;
      
    if RegKeyExists(HKCU, 'Software\JavaSoft\Prefs\partskickui') then
    begin
      Log('Removing HKCU\Software\JavaSoft\Prefs\partskickui registry key');
      RegDeleteKeyIncludingSubkeys(HKCU, 'Software\JavaSoft\Prefs\partskickui');
    end;
    
    // Check if this is an upgrade or complete uninstall
    if IsUpgrade then
    begin
      // During upgrade: PRESERVE user preferences (don't delete them)
      // Only remove PartsKick-specific preferences that might conflict
      // This preserves the PDF preferences and other user settings
      Log('UPGRADE detected - preserving user preferences (PDF preferences will be kept)');
    end
    else
    begin
      // During complete uninstall: Remove PartsKick-specific preferences (safe to delete)
      Log('COMPLETE UNINSTALL detected - removing PartsKick preferences');
      
      if RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\partskickui\db') then
      begin
        Log('Removing HKLM\Software\JavaSoft\Prefs\partskickui\db');
        RegDeleteKeyIncludingSubkeys(HKLM, 'Software\JavaSoft\Prefs\partskickui\db');
      end;
        
      if RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\partskickui\reg') then
      begin
        Log('Removing HKLM\Software\JavaSoft\Prefs\partskickui\reg');
        RegDeleteKeyIncludingSubkeys(HKLM, 'Software\JavaSoft\Prefs\partskickui\reg');
      end;
      
      if RegKeyExists(HKCU, 'Software\JavaSoft\Prefs\partskickui\db') then
      begin
        Log('Removing HKCU\Software\JavaSoft\Prefs\partskickui\db');
        RegDeleteKeyIncludingSubkeys(HKCU, 'Software\JavaSoft\Prefs\partskickui\db');
      end;
      
      if RegKeyExists(HKCU, 'Software\JavaSoft\Prefs\partskickui\reg') then
      begin
        Log('Removing HKCU\Software\JavaSoft\Prefs\partskickui\reg');
        RegDeleteKeyIncludingSubkeys(HKCU, 'Software\JavaSoft\Prefs\partskickui\reg');
      end;
      
      // Also clean up old JavaSoft preference keys that might be empty after migration
      Log('Cleaning up old JavaSoft preference keys that may be empty after migration');
      
      if RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\db') then
      begin
        Log('Removing HKLM\Software\JavaSoft\Prefs\db (old location)');
        RegDeleteKeyIncludingSubkeys(HKLM, 'Software\JavaSoft\Prefs\db');
      end;
      
      if RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\reg') then
      begin
        Log('Removing HKLM\Software\JavaSoft\Prefs\reg (old location)');
        RegDeleteKeyIncludingSubkeys(HKLM, 'Software\JavaSoft\Prefs\reg');
      end;
      
      if RegKeyExists(HKCU, 'Software\JavaSoft\Prefs\db') then
      begin
        Log('Removing HKCU\Software\JavaSoft\Prefs\db (old location)');
        RegDeleteKeyIncludingSubkeys(HKCU, 'Software\JavaSoft\Prefs\db');
      end;
      
      if RegKeyExists(HKCU, 'Software\JavaSoft\Prefs\reg') then
      begin
        Log('Removing HKCU\Software\JavaSoft\Prefs\reg (old location)');
        RegDeleteKeyIncludingSubkeys(HKCU, 'Software\JavaSoft\Prefs\reg');
      end;
    end;
    
    Log('Uninstall cleanup process completed');
  end;
end;

procedure CurStepChanged(CurStep: TSetupStep);
var
  TestValue: String;
  InstallPathValue: String;
begin
  // Installation step changed - no migration needed
  if CurStep = ssInstall then
  begin
    Log('Starting installation...');
    if IsAllUsers then
      Log('IsAllUsers: True')
    else
      Log('IsAllUsers: False');
    if IsAdmin then
      Log('IsAdmin: True')
    else
      Log('IsAdmin: False');
    if IsAdminInstallMode() then
      Log('IsAdminInstallMode: True')
    else
      Log('IsAdminInstallMode: False');
    if InstallTypePage.Values[0] then
      Log('InstallTypePage.Values[0] (Current User): True')
    else
      Log('InstallTypePage.Values[0] (Current User): False');
    if InstallTypePage.Values[1] then
      Log('InstallTypePage.Values[1] (All Users): True')
    else
      Log('InstallTypePage.Values[1] (All Users): False');
  end;
  
  if CurStep = ssPostInstall then
  begin
    Log('Installation completed...');
    
    // Create registry keys programmatically based on installation type
    if IsAdminInstallMode() then
    begin
      WriteToLogFile('Creating HKLM JavaSoft Prefs registry keys for All Users installation...');
      WriteToLogFile('Attempting to write: HKLM\Software\JavaSoft\Prefs\partskickui\InstallPath = ' + ExpandConstant('{app}'));
      if RegWriteStringValue(HKLM, 'Software\JavaSoft\Prefs\partskickui', 'InstallPath', ExpandConstant('{app}')) then
        WriteToLogFile('SUCCESS: Created HKLM\Software\JavaSoft\Prefs\partskickui\InstallPath = ' + ExpandConstant('{app}'))
      else
        WriteToLogFile('FAILED: Could not create HKLM\Software\JavaSoft\Prefs\partskickui\InstallPath');
      
      // Try to read it back immediately
      if RegQueryStringValue(HKLM, 'Software\JavaSoft\Prefs\partskickui', 'InstallPath', TestValue) then
        WriteToLogFile('READBACK SUCCESS: HKLM\Software\JavaSoft\Prefs\partskickui\InstallPath = ' + TestValue)
      else
        WriteToLogFile('READBACK FAILED: Could not read HKLM\Software\JavaSoft\Prefs\partskickui\InstallPath');
    end
    else
    begin
      WriteToLogFile('Creating HKCU JavaSoft Prefs registry keys for Current User installation...');
      if RegWriteStringValue(HKCU, 'Software\JavaSoft\Prefs\partskickui', 'InstallPath', ExpandConstant('{app}')) then
        WriteToLogFile('SUCCESS: Created HKCU\Software\JavaSoft\Prefs\partskickui\InstallPath = ' + ExpandConstant('{app}'))
      else
        WriteToLogFile('FAILED: Could not create HKCU\Software\JavaSoft\Prefs\partskickui\InstallPath');
    end;
    
    Log('Checking if registry keys were created...');
    if RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\partskickui') then
      Log('HKLM\Software\JavaSoft\Prefs\partskickui exists: True')
    else
      Log('HKLM\Software\JavaSoft\Prefs\partskickui exists: False');
    if RegKeyExists(HKCU, 'Software\JavaSoft\Prefs\partskickui') then
      Log('HKCU\Software\JavaSoft\Prefs\partskickui exists: True')
    else
      Log('HKCU\Software\JavaSoft\Prefs\partskickui exists: False');
    
    // Check if the InstallPath value was created
    if RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\partskickui') then
    begin
      Log('HKLM\Software\JavaSoft\Prefs\partskickui key exists, checking InstallPath value...');
      if RegValueExists(HKLM, 'Software\JavaSoft\Prefs\partskickui', 'InstallPath') then
        Log('InstallPath value exists: True')
      else
        Log('InstallPath value exists: False');
    end;
    
    if RegKeyExists(HKCU, 'Software\JavaSoft\Prefs\partskickui') then
    begin
      Log('HKCU\Software\JavaSoft\Prefs\partskickui key exists, checking InstallPath value...');
      if RegValueExists(HKCU, 'Software\JavaSoft\Prefs\partskickui', 'InstallPath') then
        Log('InstallPath value exists: True')
      else
        Log('InstallPath value exists: False');
    end;
    
    // Save a copy of the log to a known location
    WriteToLogFile('=== Installation Debug Summary ===');
    if IsAllUsers then
      WriteToLogFile('Final IsAllUsers: True')
    else
      WriteToLogFile('Final IsAllUsers: False');
    if IsAdmin then
      WriteToLogFile('Final IsAdmin: True')
    else
      WriteToLogFile('Final IsAdmin: False');
    if IsAdminInstallMode() then
      WriteToLogFile('Final IsAdminInstallMode: True')
    else
      WriteToLogFile('Final IsAdminInstallMode: False');
    
    // Also create a simple log file in the installation directory
    try
      SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 
        'Installation completed at ' + GetDateTimeString('yyyy-mm-dd hh:nn:ss', '-', ':') + #13#10, False);
      
      // Add debug information
      if IsAllUsers then
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'IsAllUsers: True' + #13#10, True)
      else
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'IsAllUsers: False' + #13#10, True);
        
      if IsAdmin then
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'IsAdmin: True' + #13#10, True)
      else
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'IsAdmin: False' + #13#10, True);
        
      if IsAdminInstallMode() then
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'IsAdminInstallMode: True' + #13#10, True)
      else
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'IsAdminInstallMode: False' + #13#10, True);
        
      // Check registry keys
      if RegKeyExists(HKLM, 'Software\JavaSoft\Prefs\partskickui') then
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'HKLM\Software\JavaSoft\Prefs\partskickui: EXISTS' + #13#10, True)
      else
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'HKLM\Software\JavaSoft\Prefs\partskickui: NOT FOUND' + #13#10, True);
        
      // Check if it's a 32-bit vs 64-bit issue
      if RegKeyExists(HKLM32, 'Software\JavaSoft\Prefs\partskickui') then
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'HKLM32\Software\JavaSoft\Prefs\partskickui: EXISTS' + #13#10, True)
      else
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'HKLM32\Software\JavaSoft\Prefs\partskickui: NOT FOUND' + #13#10, True);
        
      if RegKeyExists(HKLM64, 'Software\JavaSoft\Prefs\partskickui') then
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'HKLM64\Software\JavaSoft\Prefs\partskickui: EXISTS' + #13#10, True)
      else
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'HKLM64\Software\JavaSoft\Prefs\partskickui: NOT FOUND' + #13#10, True);
        
      if RegKeyExists(HKCU, 'Software\JavaSoft\Prefs\partskickui') then
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'HKCU\Software\JavaSoft\Prefs\partskickui: EXISTS' + #13#10, True)
      else
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'HKCU\Software\JavaSoft\Prefs\partskickui: NOT FOUND' + #13#10, True);
        
      // Try to read the actual InstallPath value
      if RegValueExists(HKLM, 'Software\JavaSoft\Prefs\partskickui', 'InstallPath') then
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'HKLM InstallPath value: EXISTS' + #13#10, True)
      else
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'HKLM InstallPath value: NOT FOUND' + #13#10, True);
        
      // Try to read the actual value
      if RegQueryStringValue(HKLM, 'Software\JavaSoft\Prefs\partskickui', 'InstallPath', InstallPathValue) then
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'HKLM InstallPath actual value: ' + InstallPathValue + #13#10, True)
      else
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'HKLM InstallPath actual value: COULD NOT READ' + #13#10, True);
        
      if RegValueExists(HKCU, 'Software\JavaSoft\Prefs\partskickui', 'InstallPath') then
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'HKCU InstallPath value: EXISTS' + #13#10, True)
      else
        SaveStringToFile(ExpandConstant('{app}\install_log.txt'), 'HKCU InstallPath value: NOT FOUND' + #13#10, True);
        
    except
      // Ignore errors
    end;
  end;
end;

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}";

[Files]
Source: "dist\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "dist_extra\*.ps1"; DestDir: "{app}\extra"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "D:\Hunter\devtools\customjre\*"; DestDir: "{app}\customjre"; Flags: ignoreversion recursesubdirs createallsubdirs
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Icons]
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\customjre\bin\javaw.exe"; Parameters: "-jar {app}\pk.jar {code:GetRegistryParam} fix-m-c=6"; IconFilename: {app}\pa.ico; WorkingDir: "{app}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\customjre\bin\javaw.exe"; Parameters: "-jar {app}\pk.jar {code:GetRegistryParam} fix-m-c=6"; IconFilename: {app}\pa.ico; WorkingDir: "{app}"; Tasks: desktopicon

[Run]
Filename: "{app}\customjre\bin\javaw.exe"; Parameters: "-jar {app}\pk.jar {code:GetRegistryParam} fix-m-c=6"; WorkingDir: "{app}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{app}"
