<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.1.final using JasperReports Library version 6.20.1-7584acb244139816654f64e2fd57a00d3e31921e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="AMSLineItem" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="4eedbb89-b4f6-4469-9ab6-f642a1688cf7">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="LineItemArray"/>
	<style name="Title" forecolor="#FFFFFF" fontName="Times New Roman" fontSize="50" isBold="false" pdfFontName="Times-Bold"/>
	<style name="SubTitle" forecolor="#CCCCCC" fontName="Times New Roman" fontSize="18" isBold="false" pdfFontName="Times-Roman"/>
	<style name="Column header" forecolor="#666666" fontName="Times New Roman" fontSize="14" isBold="true"/>
	<style name="Detail" mode="Transparent" fontName="Times New Roman"/>
	<style name="Row" mode="Transparent" fontName="Times New Roman" pdfFontName="Times-Roman">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style mode="Opaque" backcolor="#EEEFF0"/>
		</conditionalStyle>
	</style>
	<style name="Table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<leftPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
			<rightPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#CACED0">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style backcolor="#D8D8D8"/>
		</conditionalStyle>
	</style>
	<subDataset name="tableDataset" uuid="f13e6d36-5148-4ecc-bbe3-3035def80980">
		<queryString>
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="clientName" class="java.lang.String"/>
	<parameter name="address1" class="java.lang.String"/>
	<parameter name="address2" class="java.lang.String"/>
	<parameter name="cityPP" class="java.lang.String"/>
	<parameter name="phoneEmail" class="java.lang.String"/>
	<parameter name="RONumber" class="java.lang.String"/>
	<parameter name="claimNumber" class="java.lang.String"/>
	<parameter name="estimateId" class="java.lang.String"/>
	<parameter name="vehicle" class="java.lang.String"/>
	<parameter name="vin" class="java.lang.String"/>
	<parameter name="odometer" class="java.lang.String"/>
	<parameter name="totalLine" class="java.lang.String"/>
	<parameter name="totalQty" class="java.lang.String"/>
	<parameter name="totalAmt" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="lineNum" class="java.lang.Integer">
		<fieldDescription><![CDATA[lineNum]]></fieldDescription>
	</field>
	<field name="partsTypeName" class="java.lang.String">
		<fieldDescription><![CDATA[partsTypeName]]></fieldDescription>
	</field>
	<field name="description" class="java.lang.String">
		<fieldDescription><![CDATA[description]]></fieldDescription>
	</field>
	<field name="partsNum" class="java.lang.String">
		<fieldDescription><![CDATA[partsNum]]></fieldDescription>
	</field>
	<field name="qty" class="java.lang.Integer">
		<fieldDescription><![CDATA[qty]]></fieldDescription>
	</field>
	<field name="amtAfterDisc" class="java.lang.Double">
		<fieldDescription><![CDATA[amtAfterDisc]]></fieldDescription>
	</field>
	<sortField name="lineNum"/>
	<title>
		<band height="167" splitType="Stretch">
			<line>
				<reportElement x="0" y="120" width="556" height="1" uuid="806ce5df-1219-4876-ae0c-ca7405b1f246">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="380" y="10" width="50" height="20" uuid="0f86baff-6386-4f3f-b3fe-2388707babe8"/>
				<box rightPadding="4"/>
				<textElement textAlignment="Right"/>
				<text><![CDATA[Date:]]></text>
			</staticText>
			<textField pattern="dd MMMMM yyyy">
				<reportElement x="430" y="10" width="118" height="20" uuid="bb10dbe1-0a4f-4722-9953-c163b63cf979"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="360" y="50" width="70" height="20" uuid="0b3f9342-da78-4cfa-9fc5-2301c4749678"/>
				<box rightPadding="4"/>
				<textElement textAlignment="Right"/>
				<text><![CDATA[Estimate ID:]]></text>
			</staticText>
			<staticText>
				<reportElement x="340" y="30" width="90" height="20" uuid="af7d84e4-104d-46ea-bfff-a4c305c5867f"/>
				<box rightPadding="4"/>
				<textElement textAlignment="Right"/>
				<text><![CDATA[RO Number:]]></text>
			</staticText>
			<line>
				<reportElement x="0" y="150" width="556" height="1" uuid="771c990e-9acf-4f72-abcd-77920eabbada">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="340" y="70" width="90" height="20" uuid="144d5cb1-b9fa-494f-9b2e-78129f27ee2f"/>
				<box rightPadding="4"/>
				<textElement textAlignment="Right"/>
				<text><![CDATA[Claim Number:]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="0" width="330" height="25" uuid="736092b4-0a95-48e6-abb0-d0fa95896990"/>
				<textElement>
					<font size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{clientName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="27" width="330" height="15" uuid="e492b5ec-f637-4617-b1a9-a11efbacb857"/>
				<textFieldExpression><![CDATA[$P{address1}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="44" width="330" height="15" uuid="c7632b9c-fffa-4130-880c-cabc0580ea20">
					<printWhenExpression><![CDATA[new Boolean($P{address2} != null)]]></printWhenExpression>
				</reportElement>
				<textFieldExpression><![CDATA[$P{address2}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="61" width="330" height="15" uuid="68fa11d1-2341-45ac-8a95-0126b2942466"/>
				<textFieldExpression><![CDATA[$P{cityPP}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="79" width="330" height="15" uuid="47836b53-afaf-404e-8f46-968ef6a78bd0">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$P{phoneEmail}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="430" y="30" width="118" height="20" uuid="79ded8b0-b9e0-4523-8cd4-60d72e9cd141"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$P{RONumber}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="430" y="50" width="118" height="20" uuid="870165a6-39da-47e6-8b64-a1cc9fc7dc52"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$P{estimateId}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="430" y="70" width="118" height="20" uuid="0c763030-d2e5-4259-9623-1729543e7b82"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$P{claimNumber}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="10" y="130" width="230" height="14" uuid="d9c3da31-4d58-4fb8-9dd2-a58f414d218a">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$P{vehicle}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="250" y="130" width="140" height="14" uuid="e3572d13-c344-45fe-9465-2097ee0f808f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$P{vin}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="20">
			<staticText>
				<reportElement x="0" y="0" width="40" height="20" uuid="1070ca46-acef-4c2c-afe0-726390c27cc4">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="da61fbad-fd50-4286-bdd2-87ba04fb8bda"/>
				</reportElement>
				<textElement textAlignment="Right"/>
				<text><![CDATA[Line#]]></text>
			</staticText>
			<staticText>
				<reportElement x="60" y="0" width="70" height="20" uuid="15c50c73-6f9f-4e2e-8c25-6eb7b803d7b8">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="b647b2ad-3c5b-46a9-92f4-ab2489e85cc3"/>
				</reportElement>
				<text><![CDATA[Part Type]]></text>
			</staticText>
			<staticText>
				<reportElement x="170" y="0" width="160" height="20" uuid="eca2a3d6-1928-424a-aabd-cafb12e47e43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="647d3b51-3d41-4e17-8e4b-7d9a1011b923"/>
				</reportElement>
				<text><![CDATA[Description]]></text>
			</staticText>
			<staticText>
				<reportElement x="340" y="0" width="90" height="20" uuid="210db7de-0d29-43c3-9f4b-c0bb347736c9">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="*************-442a-a381-c53433e6c863"/>
				</reportElement>
				<text><![CDATA[Part Number]]></text>
			</staticText>
			<staticText>
				<reportElement x="440" y="0" width="40" height="20" uuid="735adcb7-1a21-4aa8-aef2-80ee004ec8f0">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="5006be26-5ce9-4ffd-9062-d813f22d6e20"/>
				</reportElement>
				<textElement textAlignment="Right"/>
				<text><![CDATA[Qty]]></text>
			</staticText>
			<staticText>
				<reportElement x="482" y="0" width="68" height="20" uuid="20e5162a-5dca-4451-8674-bfd211b612cf">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="2e32371a-93da-410c-8125-cff7ab37afcb"/>
				</reportElement>
				<textElement textAlignment="Right"/>
				<text><![CDATA[Price]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="23">
			<textField textAdjust="StretchHeight">
				<reportElement x="60" y="5" width="100" height="14" uuid="840bd0ba-4f14-48e5-adb0-bab27975325d">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="b647b2ad-3c5b-46a9-92f4-ab2489e85cc3"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{partsTypeName}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement x="170" y="5" width="160" height="14" uuid="59570d25-5cfd-4117-b47f-db38017ab7eb">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="647d3b51-3d41-4e17-8e4b-7d9a1011b923"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="340" y="5" width="90" height="14" uuid="4ef4cad6-fc22-4aa2-af49-ea7e07d38529">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="*************-442a-a381-c53433e6c863"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{partsNum}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="440" y="5" width="40" height="14" uuid="a4f4262b-77d3-41ca-8e17-7a1269e7cbf1">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="5006be26-5ce9-4ffd-9062-d813f22d6e20"/>
				</reportElement>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{qty}]]></textFieldExpression>
			</textField>
			<textField pattern="¤#,##0.###;¤(#,##0.###-)">
				<reportElement x="482" y="5" width="70" height="14" uuid="48a5cbcb-8ace-433a-9928-e6e946673021">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="2e32371a-93da-410c-8125-cff7ab37afcb"/>
				</reportElement>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{amtAfterDisc}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="3" width="40" height="16" uuid="7452c18d-c5b6-405b-bf1c-66f955a4e911">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="57d6ecce-cca8-4e74-a90a-714a650d9789"/>
				</reportElement>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{lineNum}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<lastPageFooter>
		<band height="190">
			<textField>
				<reportElement x="0" y="80" width="40" height="20" uuid="67c4c151-fb41-420f-990b-c08236a77b35"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$P{totalLine}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="440" y="80" width="40" height="20" uuid="22074586-d11b-453e-a829-f0c1183f3693"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$P{totalQty}]]></textFieldExpression>
			</textField>
			<textField pattern="¤#,##0.##;¤-#,##0.##">
				<reportElement x="482" y="80" width="70" height="20" uuid="37832b74-f9d4-4583-a10f-ac2ec2d798af"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$P{totalAmt}]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
</jasperReport>
