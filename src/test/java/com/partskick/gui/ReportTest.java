package com.partskick.gui;

import com.partskick.client.Client;
import com.partskick.report.ItemFactory;
import com.partskick.ui.LineItem;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.text.NumberFormat;
import java.util.*;

public class ReportTest {
    @Test
    public void testJasperReport() throws JRException, URISyntaxException, IOException {
/*
        Client client = new Client().setClientName("Norjon Management Associates")
                .setPhone("4162580818").setEmail("<EMAIL>")
                .setAddress1("1034 Lillian St.").setAddress2("")
                .setCity("Toronto").setProvince("ON").setPostCode("M2M3G3")
                .setContactPerson("Michael Lu");
        JasperReport jasperReport = getJasperReport();

        // 2. parameters "empty"
        Map<String, Object> parameters = getParameters(client);

        // 3. datasource "java object"
        JRDataSource dataSource = getDataSource();

        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport,
                parameters,
                dataSource);
        File home = SystemUtils.getUserHome();

        JasperExportManager.exportReportToPdfFile(jasperPrint, home.getPath() + "/test.pdf");

        if (Desktop.isDesktopSupported()) {
            Desktop.getDesktop().open(new File(home.getPath() + "/test.pdf"));
        } else {
            System.out.println("Awt Desktop is not supported!");
        }
*/
    }

    private static JasperReport getJasperReport() throws JRException, URISyntaxException {
        URI uri = ClassLoader.getSystemResource("LineItem.jrxml").toURI();
        File template = new File(uri);
        return JasperCompileManager.compileReport(template.getAbsolutePath());
    }
    private static Map<String, Object> getParameters(Client client){
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("clientName", client.getClientName());
        parameters.put("phoneEmail", client.getPhone() + "  " + client.getEmail());
        parameters.put("address1", client.getAddress1());
        parameters.put("address2", client.getAddress2());
        parameters.put("cityPP", client.getCity() + ", " + client.getProvince() + " " + client.getPostCode());
        parameters.put("contact", client.getContactPerson());
        parameters.put("RONumber", "34567");
        parameters.put("claimNumber", "c-876552");
        parameters.put("estimateId", "48596-0");
        parameters.put("vin", "VIN: AJ8SALJFADLSKJL098D");
        parameters.put("vehicle", "BMW X5 Sport 4W");
        parameters.put("odometer", "Odometer: 56,834");
        parameters.put("totalLine", "3");
        parameters.put("totalQty", "4");
        Locale ca = new Locale("en", "CA");
        NumberFormat dollarFormat = NumberFormat.getCurrencyInstance(ca);
        parameters.put("totalAmt", dollarFormat.format(123456));
        return parameters;
    }

    private static JRDataSource getDataSource(){
        List<LineItem> items = Arrays.asList(ItemFactory.getItems());
        return new JRBeanCollectionDataSource(items);
    }
}
