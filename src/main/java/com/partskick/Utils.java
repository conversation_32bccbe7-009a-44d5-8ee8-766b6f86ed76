package com.partskick;

import com.linuxense.javadbf.DBFField;
import com.linuxense.javadbf.DBFWriter;
import com.partskick.client.Client;
import com.partskick.client.ClientHelper;
import com.partskick.client.IHasOwnerId;
import com.partskick.client.Tax;
import com.partskick.common.DebugInfo;
import com.partskick.gui.progress.ProgressIndeterminateDialog;
import com.partskick.model.Estimation;
import com.partskick.parts.Invoice;
import com.partskick.parts.InvoicePreferenceClient;
import com.partskick.parts.InvoicePreferenceSupplier;
import com.partskick.parts.InvoicePreferenceV2;
import com.partskick.service.InvoiceService;
import com.partskick.ui.*;
import com.partskick.ui.pdf.OrderedParts;
import lombok.extern.slf4j.Slf4j;
import net.sf.jasperreports.engine.JRException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.swing.*;
import java.awt.*;
import java.awt.datatransfer.DataFlavor;
import java.awt.datatransfer.Transferable;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.InetAddress;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.UnknownHostException;
import java.nio.file.FileSystems;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.LinkedList;
import java.util.List;
import java.util.ResourceBundle;
import java.util.prefs.Preferences;
import java.util.regex.Pattern;

import static com.partskick.gui.invoice.InvoicePreferenceConstants.*;

@Slf4j
public class Utils {
	public static Pattern UUID_REGEX =
			Pattern.compile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");
	public static Color DEFAULT_BKC = Color.WHITE;
	public static String encryptKey = "comdotguluforpartskick";
	public static SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd/YYYY HH:mm:ss");
	public static String getFileExtension(String name) {

		int pointIndex = name.lastIndexOf(".");

		if(pointIndex == -1) {
			return null;
		}

		if(pointIndex == name.length()-1) {
			return null;
		}

		return name.substring(pointIndex+1, name.length());
	}

	public static String extract(String s, String p, String d) {
		if (StringUtils.isNotBlank(s) && StringUtils.isNotBlank(p)) {
			String[] sa = s.toUpperCase().split(p.toUpperCase());
			if (sa != null && sa.length > 1) {
				String xp = sa[1].trim();
				if (xp.startsWith(d)) {
					xp = xp.substring(1).trim();
					return xp.split("[\\,\\\n]")[0];
				}
			}
		}
		return "SN_UNKNOW";
	}

	public static ImageIcon createIcon(Class clz, String path) {
		URL url = clz.getResource(path);

		if(url == null) {
			log.error("Unable to load image: {}", path);
		}

		ImageIcon icon = new ImageIcon(url);

		return icon;
	}

	public static ImageIcon getCommonIcon(Class clz) {
		return createIcon(clz, "/images/pa_icon.png");
	}

	public static boolean writeToFile(List<String> targets, String pfileName, DBFField[] fields, List<Object[]> cellObjects, String ext) {
		return writeToFile(targets, pfileName, fields, cellObjects, ext, System.currentTimeMillis());
	}

	public static boolean writeToFile(List<String> targets, String pfileName, DBFField[] fields, List<Object[]> cellObjects, String ext, long lastModified) {
		if (!ext.startsWith(".")) ext = "." + ext;
		boolean success = true;
		for (String pathTo : targets) {
			String output = pathTo + File.separator + pfileName + ext;
			try (DBFWriter writer = new DBFWriter(new FileOutputStream(output))) {
				writer.setFields(fields);
				cellObjects.forEach(writer::addRecord);
			} catch (Exception ex) {
				log.error(ex.getMessage(), ex);
				success = false;
			}
			File file = new File(output);
			if (file.isFile()) {
				file.setLastModified(lastModified);
			}
		}
		return success;
	}

	public static String getHostname() {
		try {
			InetAddress addr = InetAddress.getLocalHost();
			return addr.getHostName();
		} catch (UnknownHostException e) {
		}
		return "";
	}

	public static String cleanPath(String s) {
		if (s == null) return "";
		return s.trim().replaceAll("\\t", "").replaceAll("\\n", "").replaceAll("\\r", "");
	}

	public static String getClipboard() {
		Transferable t = Toolkit.getDefaultToolkit().getSystemClipboard().getContents(null);
		try {
			if (t != null && t.isDataFlavorSupported(DataFlavor.stringFlavor)) {
				String text = (String) t.getTransferData(DataFlavor.stringFlavor);

				return text.trim();
			}
		} catch (Exception e) {
		}
		return "";
	}

	public static String getUUIDFromClipboard() {
		String s = getClipboard();
		return UUID_REGEX.matcher(s).matches() ? s : "";
	}

	public static Double calculateDisc(Double price, Double baseDisc, LinkedList<Tier> tieredDisc) {
		if (price == null || price <= 0) return 0d;
		if (tieredDisc == null || tieredDisc.isEmpty()) return baseDisc;
		Double disc = Double.valueOf(baseDisc);
		for (Tier tier: tieredDisc) {
			if (price <= tier.getStepAmount()) break;
			disc += tier.getDiscIncrease();
		}
		return disc;
	}

	public static String trimString(String text, int len) {
		return (StringUtils.isBlank(text) || text.length() <= len) ? text : (text.substring(0, len - 1) + " ... ");
	}
	public static String trimString(String text) {
		return trimString(text, 100);
	}

	public static boolean allowDiscountOverwrite() {
		return Boolean.parseBoolean(Global.configMap.computeIfAbsent(com.partskick.common.Global.ALLOW_DISCOUNT_OVERWRITE, k -> false).toString());
	}

	public static void setProcessType(LineItem item) {
		if (!Global.processTypes.isEmpty()) {
			for (Pair<Pattern, ProcessType> op : Global.processTypes) {
				if (op.getLeft().matcher(item.getDescription()).find()) {
					item.setAction(op.getRight());
					break;
				}
			}
		}
	}

	public static String getEmsFilePath(Estimation estimation) {
		EmsSource source = EmsSourceHelper.getMapByName().get(estimation.getSource());
		return estimation.isAdjusted() ? source.getTraderPath() : source.getExportPath();
	}

	public static String getEmsFileFullPath(Estimation estimation) {
		EmsSource source = EmsSourceHelper.getMapByName().get(estimation.getSource());
		return (estimation.isAdjusted() ? source.getTraderPath() : source.getExportPath()) + estimation.getFileName();
	}

	public static Pair<String, String> splitFullName(String fullName) {
		if (StringUtils.isBlank(fullName)) return Pair.of("", "");
		int idx = fullName.lastIndexOf(FileSystems.getDefault().getSeparator());
		return Pair.of(fullName.substring(0, idx + 1), fullName.substring(idx + 1));
	}

	public static void buildDebug(String process, String info) {
		DebugInfo debugInfo = Global.debugMap.computeIfAbsent(process, k -> new DebugInfo());
		debugInfo.addEvent(process, info, LocalDateTime.now());
	}

	public static void loadLocalInvoicePreferences(Preferences prefs) {
		Global.localInvoicePreference = new InvoicePreferenceV2();
		InvoicePreferenceClient preferenceClient = Global.localInvoicePreference.getClient();
		List<InvoicePreferenceSupplier> preferenceSuppliers = Global.localInvoicePreference.getSuppliers();
		InvoicePreferenceSupplier preferenceSupplier = new InvoicePreferenceSupplier();
		preferenceSuppliers.add(preferenceSupplier);
		preferenceSupplier.setCompanyName(prefs.get(INVOICE_SUPPLIER_NAME, ""));
		preferenceSupplier.setCompanyAddress1(prefs.get(INVOICE_SUPPLIER_ADDRESS1, ""));
		preferenceSupplier.setCompanyAddress2(prefs.get(INVOICE_SUPPLIER_ADDRESS2, ""));
		preferenceSupplier.setCompanyCity(prefs.get(INVOICE_SUPPLIER_CITY, ""));
		preferenceSupplier.setCompanyProvince(prefs.get(INVOICE_SUPPLIER_PROVINCE, ""));
		preferenceSupplier.setCompanyZip(prefs.get(INVOICE_SUPPLIER_ZIP, ""));
		preferenceSupplier.setCompanyPhone(prefs.get(INVOICE_SUPPLIER_PHONE, ""));
		preferenceSupplier.setCompanyEmail(prefs.get(INVOICE_SUPPLIER_EMAIL, ""));
		preferenceSupplier.setSupplierTaxId(prefs.get(INVOICE_SUPPLIER_TAX_ID, ""));
		preferenceSupplier.setSupplierTaxRate(prefs.getDouble(INVOICE_SUPPLIER_TAX_RATE, 0.0));
		preferenceSupplier.setSupplierTaxName(prefs.get(INVOICE_SUPPLIER_TAX_NAME, ""));
		preferenceClient.setBillToName(Global.companyInvoicePreference.getClient().getBillToName());
		preferenceClient.setBillToAddress1(Global.companyInvoicePreference.getClient().getBillToAddress1());
		preferenceClient.setBillToAddress2(Global.companyInvoicePreference.getClient().getBillToAddress2());
		preferenceClient.setBillToCity(Global.companyInvoicePreference.getClient().getBillToCity());
		preferenceClient.setBillToProvince(Global.companyInvoicePreference.getClient().getBillToProvince());
		preferenceClient.setBillToZip(Global.companyInvoicePreference.getClient().getBillToZip());
		preferenceClient.setShipToName(prefs.get(INVOICE_SHIP_TO_NAME, ""));
		preferenceClient.setShipToAddress1(prefs.get(INVOICE_SHIP_TO_ADDRESS1, ""));
		preferenceClient.setShipToAddress2(prefs.get(INVOICE_SHIP_TO_ADDRESS2, ""));
		preferenceClient.setShipToCity(prefs.get(INVOICE_SHIP_TO_CITY, ""));
		preferenceClient.setShipToProvince(prefs.get(INVOICE_SHIP_TO_PROVINCE, ""));
		preferenceClient.setShipToZip(prefs.get(INVOICE_SHIP_TO_ZIP, ""));
	}

	public static void generateInvoicePdf(JDialog parent, ProgressIndeterminateDialog progressDialog, ResourceBundle bundle, Invoice invoice, String estimation, boolean isPreview, String description) {
		SwingWorker<String, Integer> invoiceWorker = new SwingWorker<>() {
			@Override
			protected void done() {
				progressDialog.setIndeterminate(false);
				progressDialog.setVisible(false);

				if (isCancelled()) return;
				try {
					String filePath = get();
					if (isPreview) {
						if (Desktop.isDesktopSupported()) {
							Desktop.getDesktop().open(new File(filePath));
						} else {
							JOptionPane.showMessageDialog(null, bundle.getString("ms_open_error"));
						}
					} else {
						String[] options = {bundle.getString("ms_open_file"), bundle.getString("ms_open_folder"), bundle.getString("ms_cancel")};
						int ans = JOptionPane.showOptionDialog(null, bundle.getString("ms_file_saved") + filePath,
								bundle.getString("tt_file_saved"), JOptionPane.DEFAULT_OPTION, JOptionPane.INFORMATION_MESSAGE,
								null, options, options[0]);
						if (ans == 0) {
							if (Desktop.isDesktopSupported()) {
								Desktop.getDesktop().open(new File(filePath));
							} else {
								JOptionPane.showMessageDialog(null, bundle.getString("ms_open_error"));
							}
						} else if (ans == 1) {
							if (Desktop.isDesktopSupported()) {
								Runtime.getRuntime().exec("explorer.exe /select," + filePath);
							} else {
								JOptionPane.showMessageDialog(null, bundle.getString("ms_open_folder_error"));
							}
						}
					}
				} catch (Exception e) {
					log.error(e.getMessage(), e);
					JOptionPane.showMessageDialog(parent, e.getMessage());
				}
			}

			@Override
			protected String doInBackground() throws URISyntaxException, IOException, InterruptedException, JRException {
				progressDialog.setVisible(true);
				progressDialog.setIndeterminate(true);
				try {
					Thread.sleep(1000);
				} catch (InterruptedException e) {
				}
				OrderedParts orderedParts = new OrderedParts().setDescription("PN# " + invoice.getPartsNumber() + "\r\n" + description + "\r\nVin: " + invoice.getVin()).setQuantity(invoice.getQty())
						.setPrice(invoice.getPrice()).setAmount(invoice.getQty() * invoice.getPrice());
				return isPreview ? InvoiceService.generatePreview(estimation, invoice, orderedParts) : InvoiceService.generatePdf(estimation, invoice, orderedParts);
			}
		};
		invoiceWorker.execute();
	}

	public static Pair<String, Double> getTaxes(String territory) {
		if (StringUtils.isBlank(territory)) return Pair.of("", 0.0);
		List<Tax> taxes = Global.taxes.stream().filter(t -> t.getProvinceState().equalsIgnoreCase(territory)).toList();
		if (taxes.isEmpty()) {
			return Pair.of("", 0.0);
		}
		return Pair.of(taxes.stream().map(Tax::getName).reduce("", (a, b) -> StringUtils.isBlank(a) ? b : a + "," + b), taxes.stream().mapToDouble(Tax::getRate).sum());
	}

	public static String findParameter(String[] args, String key) {
		for (String arg : args) {
			if (arg.startsWith(key + "=")) {
				return arg.substring((key + "=").length());
			}
		}
		return null;
	}

	public static boolean isSameOwnerParts(String partsClientId) {
		if (!Global.clients.containsKey(partsClientId)) return false;
		Long ownerId = Global.clients.get(partsClientId).getOwnerId();
		Client my = ClientHelper.getClient();
		if (my == null) return false;
		if (my instanceof IHasOwnerId client) {
			return client.getOwnerId().equals(ownerId);
		}
		return false;
	}

	/**
	 * Validates an email address format
	 *
	 * @param email The email address to validate
	 * @return true if the email is valid, false otherwise
	 */
	public static boolean isValidEmail(String email) {
		if (StringUtils.isBlank(email)) return false;
		return email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
	}

	/**
	 * Gets the PartsKick-specific preferences node based on installation type.
	 * @param useSystemRoot true for system-wide installation (HKLM), false for user-specific (HKCU)
	 * @return Preferences node for PartsKick database preferences
	 */
	public static Preferences getPartsKickPrefs(boolean useSystemRoot) {
		if (useSystemRoot) {
			log.debug("Using system-wide PartsKick preferences (HKLM)");
			return Preferences.systemRoot().node("partskickui").node("db");
		} else {
			log.debug("Using user-specific PartsKick preferences (HKCU)");
			return Preferences.userRoot().node("partskickui").node("db");
		}
	}
	
	/**
	 * Gets the PartsKick-specific registry preferences node based on installation type.
	 * @param useSystemRoot true for system-wide installation (HKLM), false for user-specific (HKCU)
	 * @return Preferences node for PartsKick registry preferences
	 */
	public static Preferences getPartsKickRegPrefs(boolean useSystemRoot) {
		if (useSystemRoot) {
			log.debug("Using system-wide PartsKick registry preferences (HKLM)");
			return Preferences.systemRoot().node("partskickui").node("reg");
		} else {
			log.debug("Using user-specific PartsKick registry preferences (HKCU)");
			return Preferences.userRoot().node("partskickui").node("reg");
		}
	}
	
	/**
	 * Gets the PartsKick-specific preferences node.
	 * This method is deprecated - use getPartsKickPrefs(boolean useSystemRoot) instead.
	 * Uses the global installation type if set, otherwise defaults to user-specific preferences.
	 * 
	 * @deprecated Use getPartsKickPrefs(boolean useSystemRoot) instead
	 * @return Preferences node for PartsKick database preferences
	 */
	@Deprecated
	public static Preferences getPartsKickPrefs() {
		log.warn("Using deprecated getPartsKickPrefs() - should use getPartsKickPrefs(boolean useSystemRoot)");
		try {
			return getPartsKickPrefs(getInstallationType());
		} catch (IllegalStateException e) {
			log.warn("Installation type not set, defaulting to user-specific preferences");
			return getPartsKickPrefs(false); // Default to user preferences for backward compatibility
		}
	}
	
	/**
	 * Gets the PartsKick-specific registry preferences node.
	 * This method is deprecated - use getPartsKickRegPrefs(boolean useSystemRoot) instead.
	 * Uses the global installation type if set, otherwise defaults to user-specific preferences.
	 * 
	 * @deprecated Use getPartsKickRegPrefs(boolean useSystemRoot) instead
	 * @return Preferences node for PartsKick registry preferences
	 */
	@Deprecated
	public static Preferences getPartsKickRegPrefs() {
		log.warn("Using deprecated getPartsKickRegPrefs() - should use getPartsKickRegPrefs(boolean useSystemRoot)");
		try {
			return getPartsKickRegPrefs(getInstallationType());
		} catch (IllegalStateException e) {
			log.warn("Installation type not set, defaulting to user-specific preferences");
			return getPartsKickRegPrefs(false); // Default to user preferences for backward compatibility
		}
	}

	/**
	 * Shows a PDF file chooser dialog with preference-based directory selection.
	 * The method follows this priority for the initial directory:
	 * 1. Check Windows registry preference "ems_pdf_path" if it exists and is valid
	 * 2. Use "%USERPROFILE%/Downloads" if it exists
	 * 3. Use current working directory as fallback
	 * 
	 * After user selects a file, the directory is saved to the preference for next use.
	 * 
	 * @param parent The parent component for the dialog
	 * @param title The dialog title
	 * @param bundle Resource bundle for internationalization
	 * @return The selected file, or null if cancelled
	 */
	public static File showPdfFileChooser(Component parent, String title, ResourceBundle bundle) {
		// Use the global installation type for preference access
		Preferences prefs;
		try {
			prefs = getPartsKickPrefs(getInstallationType());
		} catch (IllegalStateException e) {
			log.warn("Installation type not set, using user-specific preferences for PDF chooser");
			prefs = getPartsKickPrefs(false);
		}
		
		String emsPdfPathKey = com.partskick.gui.prefs.PrefsDialog.EMS_PDF_PATH;
		String savedPath = prefs.get(emsPdfPathKey, null);
		
		// Determine initial directory based on priority:
		// 1. Saved preference path (if valid directory)
		// 2. User's Downloads folder
		// 3. Current working directory
		File initialDirectory = null;
		
		if (savedPath != null) {
			File savedDir = new File(savedPath);
			if (savedDir.exists() && savedDir.isDirectory()) {
				initialDirectory = savedDir;
				log.debug("Using saved PDF path: {}", savedPath);
			} else {
				log.debug("Saved PDF path is not a valid directory: {}", savedPath);
			}
		}
		
		if (initialDirectory == null) {
			// Try user's Downloads folder
			String userHome = System.getProperty("user.home");
			File downloadsDir = new File(userHome, "Downloads");
			if (downloadsDir.exists() && downloadsDir.isDirectory()) {
				initialDirectory = downloadsDir;
				log.debug("Using Downloads folder: {}", downloadsDir.getAbsolutePath());
			} else {
				log.debug("Downloads folder not found: {}", downloadsDir.getAbsolutePath());
			}
		}
		
		if (initialDirectory == null) {
			// Fall back to current working directory
			initialDirectory = new File(System.getProperty("user.dir"));
			log.debug("Using current working directory: {}", initialDirectory.getAbsolutePath());
		}
		
		JFileChooser fileChooser = new JFileChooser(initialDirectory);
		fileChooser.setDialogTitle(title);
		fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter(
				bundle.getString("pdf_file_filter"), "pdf"));
		
		int returnValue = fileChooser.showOpenDialog(parent);
		
		if (returnValue == JFileChooser.APPROVE_OPTION) {
			File selectedFile = fileChooser.getSelectedFile();
			if (selectedFile != null && selectedFile.exists()) {
				// Save the directory of the selected file for future use
				File parentDir = selectedFile.getParentFile();
				if (parentDir != null && parentDir.exists() && parentDir.isDirectory()) {
					// Use admin privilege check for saving preferences
					boolean success = putPreferenceWithAdminCheck(emsPdfPathKey, parentDir.getAbsolutePath(), parent, bundle);
					if (success) {
						log.debug("PDF directory preference saved successfully");
					} else {
						log.warn("Failed to save PDF directory preference - user cancelled or admin privileges required");
					}
				}
			}
			return selectedFile;
		}
		
		return null;
	}

	/**
	 * Migrates preferences from the old JavaSoft location to the new PartsKick-specific location.
	 * This is a fallback migration that runs during application startup if needed.
	 * @param useSystemRoot true for system-wide installation (HKLM), false for user-specific (HKCU)
	 */
	public static void migratePreferencesFromOldLocation(boolean useSystemRoot) {
		try {
			log.info("Checking for preference migration from old JavaSoft location to {} location", 
					useSystemRoot ? "system-wide (HKLM)" : "user-specific (HKCU)");
			
			// Get the target preferences locations based on installation type
			Preferences newDbPrefs = getPartsKickPrefs(useSystemRoot);
			Preferences newRegPrefs = getPartsKickRegPrefs(useSystemRoot);
			log.info("Target db preference location: {}", newDbPrefs.absolutePath());
			log.info("Target reg preference location: {}", newRegPrefs.absolutePath());
			
			// Check if migration has already been completed
			String migrationFlag = newDbPrefs.get("migration_completed", null);
			if ("true".equals(migrationFlag)) {
				log.info("Migration already completed for {} location, skipping", 
						useSystemRoot ? "system-wide" : "user-specific");
				return;
			}
			log.info("Migration not completed, proceeding with migration");
			
			boolean migrationPerformed = false;
			
			// Migrate from the old db location to the new db location
			Preferences oldDbPrefs = Preferences.userRoot().node("db");
			log.info("Source db preference location: {}", oldDbPrefs.absolutePath());
			
			if (oldDbPrefs.keys().length > 0) {
				log.info("Found old JavaSoft db preferences, migrating to {} location", 
						useSystemRoot ? "system-wide" : "user-specific");
				log.info("All keys in old db location: {}", String.join(", ", oldDbPrefs.keys()));
				for (String key : oldDbPrefs.keys()) {
					log.debug("Checking db key: {}", key);
					// Migrate ALL keys from the old location (they are all PartsKick-related)
					String value = oldDbPrefs.get(key, null);
					if (value != null) {
						log.info("Found db key to migrate: {} = {}", key, value);
						// For migration, we'll use direct writes since this is a one-time operation
						// and we don't want to show dialogs during startup
						try {
							log.info("Attempting to write {} = {} to new db location", key, value);
							newDbPrefs.put(key, value);
							log.info("Successfully migrated db preference: {} = {}", key, value);
							migrationPerformed = true;
						} catch (SecurityException e) {
							log.warn("Failed to migrate db preference {} = {} - admin privileges required: {}", key, value, e.getMessage());
							// Continue with other preferences even if some fail
						} catch (Exception e) {
							log.error("Unexpected error migrating db preference {} = {}: {}", key, value, e.getMessage());
						}
					}
				}
			} else {
				log.info("No keys found in old db preferences location");
			}
			
			// Migrate from the old reg location to the new reg location
			Preferences oldRegPrefs = Preferences.userRoot().node("reg");
			log.info("Source reg preference location: {}", oldRegPrefs.absolutePath());
			
			if (oldRegPrefs.keys().length > 0) {
				log.info("Found old JavaSoft reg preferences, migrating to {} location", 
						useSystemRoot ? "system-wide" : "user-specific");
				log.info("All keys in old reg location: {}", String.join(", ", oldRegPrefs.keys()));
				for (String key : oldRegPrefs.keys()) {
					log.debug("Checking reg key: {}", key);
					// Migrate ALL keys from the old reg location
					String value = oldRegPrefs.get(key, null);
					if (value != null) {
						log.info("Found reg key to migrate: {} = {}", key, value);
						try {
							log.info("Attempting to write {} = {} to new reg location", key, value);
							newRegPrefs.put(key, value);
							log.info("Successfully migrated reg preference: {} = {}", key, value);
							migrationPerformed = true;
						} catch (SecurityException e) {
							log.warn("Failed to migrate reg preference {} = {} - admin privileges required: {}", key, value, e.getMessage());
						} catch (Exception e) {
							log.error("Unexpected error migrating reg preference {} = {}: {}", key, value, e.getMessage());
						}
					}
				}
			} else {
				log.info("No keys found in old reg preferences location");
			}
			
			// NEW: Also migrate from the opposite installation type's PartsKick preferences
			// This handles the case where user switches from "Current User" to "All Users" or vice versa
			boolean crossLocationMigrationPerformed = migrateFromOppositeInstallationType(useSystemRoot);
			if (crossLocationMigrationPerformed) {
				migrationPerformed = true;
			}
			

			
			// Mark migration as completed
			try {
				newDbPrefs.put("migration_completed", "true");
				log.info("Preference migration completed successfully for {} location", 
						useSystemRoot ? "system-wide" : "user-specific");
			} catch (SecurityException e) {
				log.warn("Failed to set migration completion flag - admin privileges required: {}", e.getMessage());
			} catch (Exception e) {
				log.error("Unexpected error setting migration completion flag: {}", e.getMessage());
			}
			
			// Clean up old preferences immediately after successful migration
			if (migrationPerformed) {
				log.info("Migration performed - cleaning up old preferences");
				cleanupOldPreferences();
			} else {
				log.debug("No migration needed for {} location - marking as completed", 
						useSystemRoot ? "system-wide" : "user-specific");
			}
		} catch (Exception e) {
			log.warn("Preference migration failed for {} location: {}", 
					useSystemRoot ? "system-wide" : "user-specific", e.getMessage());
		}
	}
	
	/**
	 * Migrates preferences from the old JavaSoft location to the new PartsKick-specific location.
	 * This method is deprecated - use migratePreferencesFromOldLocation(boolean useSystemRoot) instead.
	 * Uses the global installation type if set, otherwise defaults to user-specific migration.
	 * 
	 * @deprecated Use migratePreferencesFromOldLocation(boolean useSystemRoot) instead
	 */
	@Deprecated
	public static void migratePreferencesFromOldLocation() {
		log.warn("Using deprecated migratePreferencesFromOldLocation() - should use migratePreferencesFromOldLocation(boolean useSystemRoot)");
		try {
			migratePreferencesFromOldLocation(getInstallationType());
		} catch (IllegalStateException e) {
			log.warn("Installation type not set, defaulting to user-specific migration");
			migratePreferencesFromOldLocation(false); // Default to user-specific migration for backward compatibility
		}
	}
	
	/**
	 * Migrates preferences from the opposite installation type's PartsKick location.
	 * This handles the case where user switches from "Current User" to "All Users" or vice versa.
	 * @param useSystemRoot true for system-wide installation (HKLM), false for user-specific (HKCU)
	 * @return true if migration was performed, false otherwise
	 */
	private static boolean migrateFromOppositeInstallationType(boolean useSystemRoot) {
		try {
			log.info("Checking for cross-location migration from {} to {}", 
					useSystemRoot ? "user-specific (HKCU)" : "system-wide (HKLM)",
					useSystemRoot ? "system-wide (HKLM)" : "user-specific (HKCU)");
			
			// Get the source preferences from the opposite installation type
			Preferences sourceDbPrefs = getPartsKickPrefs(!useSystemRoot);
			Preferences sourceRegPrefs = getPartsKickRegPrefs(!useSystemRoot);
			
			// Get the target preferences for the current installation type
			Preferences targetDbPrefs = getPartsKickPrefs(useSystemRoot);
			Preferences targetRegPrefs = getPartsKickRegPrefs(useSystemRoot);
			
			log.info("Migration direction: useSystemRoot={}, source={}, target={}", 
					useSystemRoot, !useSystemRoot, useSystemRoot);
			log.info("Source will be: {}", useSystemRoot ? "HKCU" : "HKLM");
			log.info("Target will be: {}", useSystemRoot ? "HKLM" : "HKCU");
			
			log.info("Source db preference location: {}", sourceDbPrefs.absolutePath());
			log.info("Source reg preference location: {}", sourceRegPrefs.absolutePath());
			log.info("Target db preference location: {}", targetDbPrefs.absolutePath());
			log.info("Target reg preference location: {}", targetRegPrefs.absolutePath());
			
			// Check if cross-location migration has already been completed for this target location
			String crossMigrationFlag = targetDbPrefs.get("cross_migration_completed", null);
			log.info("Cross-migration flag check: '{}' (null means not set)", crossMigrationFlag);
			
			// Check if there are actually keys to migrate from the source location
			boolean hasSourceKeys = false;
			try {
				hasSourceKeys = (sourceDbPrefs.keys().length > 0 || sourceRegPrefs.keys().length > 0);
				log.info("Source location has keys to migrate: {}", hasSourceKeys);
			} catch (Exception e) {
				log.warn("Could not check source keys: {}", e.getMessage());
			}
			
			if ("true".equals(crossMigrationFlag) && !hasSourceKeys) {
				log.info("Cross-location migration already completed for {} location and no source keys found, skipping", 
						useSystemRoot ? "system-wide" : "user-specific");
				return false;
			}
			
			if ("true".equals(crossMigrationFlag) && hasSourceKeys) {
				log.info("Cross-location migration flag is set but source keys found - proceeding with migration anyway");
			} else {
				log.info("Cross-location migration not completed, proceeding with migration");
			}
			
			boolean migrationPerformed = false;
			
			// Migrate db preferences from opposite installation type
			try {
				log.info("Checking source db preferences - keys count: {}", sourceDbPrefs.keys().length);
				if (sourceDbPrefs.keys().length > 0) {
					log.info("Found PartsKick db preferences in opposite location, migrating to {} location", 
							useSystemRoot ? "system-wide" : "user-specific");
					log.info("All keys in source db location: {}", String.join(", ", sourceDbPrefs.keys()));
					
					for (String key : sourceDbPrefs.keys()) {
						// Skip migration flag keys to avoid conflicts
						if ("migration_completed".equals(key) || "cross_migration_completed".equals(key)) {
							log.debug("Skipping migration flag key: {}", key);
							continue;
						}
						
						String value = sourceDbPrefs.get(key, null);
						if (value != null) {
							log.info("Found db key to migrate: {} = {}", key, value);
							try {
								log.info("Attempting to write {} = {} to target db location", key, value);
								targetDbPrefs.put(key, value);
								log.info("Successfully migrated db preference: {} = {}", key, value);
								migrationPerformed = true;
							} catch (SecurityException e) {
								log.warn("Failed to migrate db preference {} = {} - admin privileges required: {}", key, value, e.getMessage());
							} catch (Exception e) {
								log.error("Unexpected error migrating db preference {} = {}: {}", key, value, e.getMessage());
							}
						}
					}
				} else {
					log.info("No keys found in source db preferences location");
				}
			} catch (Exception e) {
				log.warn("Failed to access source db preferences: {}", e.getMessage());
			}
			
			// Migrate reg preferences from opposite installation type
			try {
				log.info("Checking source reg preferences - keys count: {}", sourceRegPrefs.keys().length);
				if (sourceRegPrefs.keys().length > 0) {
					log.info("Found PartsKick reg preferences in opposite location, migrating to {} location", 
							useSystemRoot ? "system-wide" : "user-specific");
					log.info("All keys in source reg location: {}", String.join(", ", sourceRegPrefs.keys()));
					
					for (String key : sourceRegPrefs.keys()) {
						// Skip migration flag keys to avoid conflicts
						if ("migration_completed".equals(key) || "cross_migration_completed".equals(key)) {
							log.debug("Skipping migration flag key: {}", key);
							continue;
						}
						
						String value = sourceRegPrefs.get(key, null);
						if (value != null) {
							log.info("Found reg key to migrate: {} = {}", key, value);
							try {
								log.info("Attempting to write {} = {} to target reg location", key, value);
								targetRegPrefs.put(key, value);
								log.info("Successfully migrated reg preference: {} = {}", key, value);
								migrationPerformed = true;
							} catch (SecurityException e) {
								log.warn("Failed to migrate reg preference {} = {} - admin privileges required: {}", key, value, e.getMessage());
							} catch (Exception e) {
								log.error("Unexpected error migrating reg preference {} = {}: {}", key, value, e.getMessage());
							}
						}
					}
				} else {
					log.info("No keys found in source reg preferences location");
				}
			} catch (Exception e) {
				log.warn("Failed to access source reg preferences: {}", e.getMessage());
			}
			
			// Mark cross-location migration as completed
			if (migrationPerformed) {
				try {
					targetDbPrefs.put("cross_migration_completed", "true");
					log.info("Cross-location migration completed successfully");
				} catch (SecurityException e) {
					log.warn("Failed to set cross-migration completion flag - admin privileges required: {}", e.getMessage());
				} catch (Exception e) {
					log.error("Unexpected error setting cross-migration completion flag: {}", e.getMessage());
				}
			} else {
				log.info("No cross-location migration needed");
			}
			
			return migrationPerformed;
		} catch (Exception e) {
			log.warn("Cross-location migration failed: {}", e.getMessage());
			return false;
		}
	}
	

	
	/**
	 * Cleans up old JavaSoft preferences to avoid confusion.
	 * Removes ALL PartsKick-related preferences from the old location.
	 */
	private static void cleanupOldPreferences() {
		try {
			// Clean up user db preferences
			Preferences oldUserDbPrefs = Preferences.userRoot().node("db");
			if (oldUserDbPrefs.keys().length > 0) {
				log.info("Cleaning up old user db preferences: {}", String.join(", ", oldUserDbPrefs.keys()));
				for (String key : oldUserDbPrefs.keys()) {
					try {
						oldUserDbPrefs.remove(key);
						log.debug("Removed old user db key: {}", key);
					} catch (Exception e) {
						log.warn("Failed to remove old user db key {}: {}", key, e.getMessage());
					}
				}
				log.info("Cleaned up old user db preferences");
			}
			
			// Clean up user reg preferences
			Preferences oldUserRegPrefs = Preferences.userRoot().node("reg");
			if (oldUserRegPrefs.keys().length > 0) {
				log.info("Cleaning up old user reg preferences: {}", String.join(", ", oldUserRegPrefs.keys()));
				for (String key : oldUserRegPrefs.keys()) {
					try {
						oldUserRegPrefs.remove(key);
						log.debug("Removed old user reg key: {}", key);
					} catch (Exception e) {
						log.warn("Failed to remove old user reg key {}: {}", key, e.getMessage());
					}
				}
				log.info("Cleaned up old user reg preferences");
			}
			
			// Clean up system db preferences
			Preferences oldSystemDbPrefs = Preferences.systemRoot().node("db");
			if (oldSystemDbPrefs.keys().length > 0) {
				log.info("Cleaning up old system db preferences: {}", String.join(", ", oldSystemDbPrefs.keys()));
				for (String key : oldSystemDbPrefs.keys()) {
					try {
						oldSystemDbPrefs.remove(key);
						log.debug("Removed old system db key: {}", key);
					} catch (Exception e) {
						log.warn("Failed to remove old system db key {}: {}", key, e.getMessage());
					}
				}
				log.info("Cleaned up old system db preferences");
			}
			
			// Clean up system reg preferences
			Preferences oldSystemRegPrefs = Preferences.systemRoot().node("reg");
			if (oldSystemRegPrefs.keys().length > 0) {
				log.info("Cleaning up old system reg preferences: {}", String.join(", ", oldSystemRegPrefs.keys()));
				for (String key : oldSystemRegPrefs.keys()) {
					try {
						oldSystemRegPrefs.remove(key);
						log.debug("Removed old system reg key: {}", key);
					} catch (Exception e) {
						log.warn("Failed to remove old system reg key {}: {}", key, e.getMessage());
					}
				}
				log.info("Cleaned up old system reg preferences");
			}
			

			
			log.info("Old preference cleanup completed successfully");
		} catch (Exception e) {
			log.warn("Old preference cleanup failed: {}", e.getMessage());
		}
	}
	
	// Global installation type tracking
	private static Boolean useSystemRoot = null;
	
	/**
	 * Sets the global installation type for preference access.
	 * This should be called once during application startup.
	 * @param useSystemRoot true for system-wide installation (HKLM), false for user-specific (HKCU)
	 */
	public static void setInstallationType(boolean useSystemRoot) {
		Utils.useSystemRoot = useSystemRoot;
		log.info("Installation type set to: {}", useSystemRoot ? "system-wide (HKLM)" : "user-specific (HKCU)");
	}
	
	/**
	 * Gets the global installation type for preference access.
	 * @return true for system-wide installation (HKLM), false for user-specific (HKCU)
	 * @throws IllegalStateException if installation type has not been set
	 */
	public static boolean getInstallationType() {
		if (useSystemRoot == null) {
			throw new IllegalStateException("Installation type has not been set. Call setInstallationType() first.");
		}
		return useSystemRoot;
	}

	/**
	 * Tests if admin privileges are available for writing to HKLM.
	 * This can be used to inform users about privilege requirements.
	 * 
	 * @return true if admin privileges are available for HKLM writes, false otherwise
	 */
	public static boolean canWriteToSystemPreferences() {
		try {
			boolean useSystemRoot = getInstallationType();
			if (!useSystemRoot) {
				// User-specific installation doesn't need admin privileges
				return true;
			}
			
			// Test write to HKLM
			Preferences systemPrefs = getPartsKickPrefs(true);
			String testKey = "_admin_test_" + System.currentTimeMillis();
			systemPrefs.put(testKey, "test");
			systemPrefs.remove(testKey); // Clean up
			log.debug("Admin privileges confirmed - can write to system-wide preferences");
			return true;
		} catch (SecurityException e) {
			log.debug("Admin privileges not available - cannot write to system-wide preferences: {}", e.getMessage());
			return false;
		} catch (Exception e) {
			log.debug("Error testing admin privileges: {}", e.getMessage());
			return false;
		}
	}
	
	/**
	 * Shows a dialog informing the user that admin privileges are required for system-wide installations.
	 * Offers options to restart with admin privileges or cancel.
	 * 
	 * @param parent the parent component for the dialog
	 * @param bundle the resource bundle for localized messages
	 * @return true if user chooses to restart, false if user cancels
	 */
	public static boolean showAdminPrivilegeRequiredDialog(Component parent, ResourceBundle bundle) {
		try {
			boolean useSystemRoot = getInstallationType();
			if (!useSystemRoot) {
				// User-specific installation doesn't need admin privileges
				return false;
			}
			
			if (!canWriteToSystemPreferences()) {
				String title = bundle.getString("admin_privilege_required_title");
				String message = bundle.getString("admin_privilege_required_message");
				
				// Create custom dialog with multiple options
				Object[] options = {
					bundle.getString("admin_privilege_restart_now"),
					bundle.getString("admin_privilege_restart_manual"),
					bundle.getString("admin_privilege_cancel")
				};
				
				int result = JOptionPane.showOptionDialog(parent, message, title, 
					JOptionPane.YES_NO_CANCEL_OPTION, JOptionPane.WARNING_MESSAGE, null, options, options[0]);
				
				if (result == 0) {
					// User chose "Restart Now" - try dynamic restart
					log.info("User chose to restart now with admin privileges");
					return restartWithAdminPrivileges();
				} else if (result == 1) {
					// User chose "Restart Manual" - just return true to indicate restart needed
					log.info("User chose to restart manually with admin privileges");
					return true;
				} else {
					// User cancelled
					log.info("User cancelled admin privilege restart");
					return false;
				}
			}
		} catch (Exception e) {
			log.warn("Failed to show admin privilege dialog: {}", e.getMessage());
		}
		return false;
	}
	
	/**
	 * Attempts to write a preference value with admin privilege detection.
	 * For system-wide installations, checks admin privileges and shows dialog if needed.
	 * 
	 * @param key the preference key
	 * @param value the preference value
	 * @param parent the parent component for dialogs
	 * @param bundle the resource bundle for localized messages
	 * @return true if write was successful or user chose to restart, false if user cancelled
	 */
	public static boolean putPreferenceWithAdminCheck(String key, String value, Component parent, ResourceBundle bundle) {
		try {
			boolean useSystemRoot = getInstallationType();
			
			if (useSystemRoot) {
				// System-wide installation: try HKLM first
				try {
					Preferences systemPrefs = getPartsKickPrefs(true);
					systemPrefs.put(key, value);
					log.debug("Successfully wrote to system-wide preferences (HKLM)");
					return true;
				} catch (SecurityException e) {
					log.warn("Failed to write to system-wide preferences (HKLM) - admin privileges required");
					
					// Show dialog asking user to restart with admin privileges
					boolean restart = showAdminPrivilegeRequiredDialog(parent, bundle);
					if (restart) {
						log.info("User chose to restart with admin privileges");
						// Return true to indicate the operation should be retried after restart
						return true;
					} else {
						log.info("User cancelled - preference not saved");
						return false;
					}
				}
			} else {
				// User-specific installation: write to HKCU
				log.debug("Writing user-specific preference: {} = {}", key, value);
				Preferences userPrefs = getPartsKickPrefs(false);
				userPrefs.put(key, value);
				return true;
			}
		} catch (Exception e) {
			log.error("Failed to write preference {} = {}: {}", key, value, e.getMessage());
			return false;
		}
	}

	/**
	 * Attempts to restart the application with administrator privileges.
	 * This uses Windows UAC elevation to restart the current application.
	 * 
	 * @return true if restart was initiated, false if failed
	 */
	public static boolean restartWithAdminPrivileges() {
		try {
			// Get the current Java command
			String javaHome = System.getProperty("java.home");
			String javaBin = javaHome + File.separator + "bin" + File.separator + "java";
			
			// Get the current classpath and main class
			String classpath = System.getProperty("java.class.path");
			String mainClass = "com.partskick.gui.App"; // Adjust if needed
			
			// Build the command
			StringBuilder command = new StringBuilder();
			command.append("\"").append(javaBin).append("\"");
			command.append(" -cp \"").append(classpath).append("\"");
			command.append(" ").append(mainClass);
			
			// Add any existing command line arguments
			String[] args = getCurrentCommandLineArgs();
			for (String arg : args) {
				command.append(" ").append(arg);
			}
			
			log.info("Restarting application with admin privileges: {}", command.toString());
			
			// Use Windows runas command to elevate privileges
			String[] cmdArray = {
				"cmd", "/c", "runas", "/user:Administrator", 
				"\"cmd /c start " + command.toString() + "\""
			};
			
			ProcessBuilder pb = new ProcessBuilder(cmdArray);
			Process process = pb.start();
			
			// Wait a bit to see if it starts successfully
			Thread.sleep(2000);
			
			if (process.isAlive()) {
				log.info("Application restart with admin privileges initiated successfully");
				// Exit current application
				System.exit(0);
				return true;
			} else {
				log.warn("Failed to restart application with admin privileges");
				return false;
			}
			
		} catch (Exception e) {
			log.error("Error restarting application with admin privileges: {}", e.getMessage());
			return false;
		}
	}
	
	/**
	 * Gets the current command line arguments.
	 * This is a simplified version - you might need to store args during startup.
	 * 
	 * @return array of command line arguments
	 */
	private static String[] getCurrentCommandLineArgs() {
		// This is a simplified approach - in practice, you'd want to store
		// the original args when the application starts
		return new String[0];
	}

	/**
	 * Attempts to write a boolean preference value with admin privilege detection.
	 * For system-wide installations, checks admin privileges and shows dialog if needed.
	 * 
	 * @param key the preference key
	 * @param value the preference value
	 * @param parent the parent component for dialogs
	 * @param bundle the resource bundle for localized messages
	 * @return true if write was successful or user chose to restart, false if user cancelled
	 */
	public static boolean putBooleanPreferenceWithAdminCheck(String key, boolean value, Component parent, ResourceBundle bundle) {
		try {
			boolean useSystemRoot = getInstallationType();
			
			if (useSystemRoot) {
				// System-wide installation: try HKLM first
				try {
					Preferences systemPrefs = getPartsKickPrefs(true);
					systemPrefs.putBoolean(key, value);
					log.debug("Successfully wrote boolean to system-wide preferences (HKLM): {} = {}", key, value);
					return true;
				} catch (SecurityException e) {
					log.warn("Failed to write boolean to system-wide preferences (HKLM) - admin privileges required: {} = {}", key, value);
					
					// Show dialog asking user to restart with admin privileges
					boolean restart = showAdminPrivilegeRequiredDialog(parent, bundle);
					if (restart) {
						log.info("User chose to restart with admin privileges");
						return true;
					} else {
						log.info("User cancelled - boolean preference not saved: {} = {}", key, value);
						return false;
					}
				}
			} else {
				// User-specific installation: write to HKCU
				log.debug("Writing boolean to user-specific preferences: {} = {}", key, value);
				Preferences userPrefs = getPartsKickPrefs(false);
				userPrefs.putBoolean(key, value);
				return true;
			}
		} catch (Exception e) {
			log.error("Failed to write boolean preference {} = {}: {}", key, value, e.getMessage());
			return false;
		}
	}
	
	/**
	 * Attempts to write an integer preference value with admin privilege detection.
	 * For system-wide installations, checks admin privileges and shows dialog if needed.
	 * 
	 * @param key the preference key
	 * @param value the preference value
	 * @param parent the parent component for dialogs
	 * @param bundle the resource bundle for localized messages
	 * @return true if write was successful or user chose to restart, false if user cancelled
	 */
	public static boolean putIntPreferenceWithAdminCheck(String key, int value, Component parent, ResourceBundle bundle) {
		try {
			boolean useSystemRoot = getInstallationType();
			
			if (useSystemRoot) {
				// System-wide installation: try HKLM first
				try {
					Preferences systemPrefs = getPartsKickPrefs(true);
					systemPrefs.putInt(key, value);
					log.debug("Successfully wrote int to system-wide preferences (HKLM): {} = {}", key, value);
					return true;
				} catch (SecurityException e) {
					log.warn("Failed to write int to system-wide preferences (HKLM) - admin privileges required: {} = {}", key, value);
					
					// Show dialog asking user to restart with admin privileges
					boolean restart = showAdminPrivilegeRequiredDialog(parent, bundle);
					if (restart) {
						log.info("User chose to restart with admin privileges");
						return true;
					} else {
						log.info("User cancelled - int preference not saved: {} = {}", key, value);
						return false;
					}
				}
			} else {
				// User-specific installation: write to HKCU
				log.debug("Writing int to user-specific preferences: {} = {}", key, value);
				Preferences userPrefs = getPartsKickPrefs(false);
				userPrefs.putInt(key, value);
				return true;
			}
		} catch (Exception e) {
			log.error("Failed to write int preference {} = {}: {}", key, value, e.getMessage());
			return false;
		}
	}
	
	/**
	 * Attempts to write a double preference value with admin privilege detection.
	 * For system-wide installations, checks admin privileges and shows dialog if needed.
	 * 
	 * @param key the preference key
	 * @param value the preference value
	 * @param parent the parent component for dialogs
	 * @param bundle the resource bundle for localized messages
	 * @return true if write was successful or user chose to restart, false if user cancelled
	 */
	public static boolean putDoublePreferenceWithAdminCheck(String key, double value, Component parent, ResourceBundle bundle) {
		try {
			boolean useSystemRoot = getInstallationType();
			
			if (useSystemRoot) {
				// System-wide installation: try HKLM first
				try {
					Preferences systemPrefs = getPartsKickPrefs(true);
					systemPrefs.putDouble(key, value);
					log.debug("Successfully wrote double to system-wide preferences (HKLM): {} = {}", key, value);
					return true;
				} catch (SecurityException e) {
					log.warn("Failed to write double to system-wide preferences (HKLM) - admin privileges required: {} = {}", key, value);
					
					// Show dialog asking user to restart with admin privileges
					boolean restart = showAdminPrivilegeRequiredDialog(parent, bundle);
					if (restart) {
						log.info("User chose to restart with admin privileges");
						return true;
					} else {
						log.info("User cancelled - double preference not saved: {} = {}", key, value);
						return false;
					}
				}
			} else {
				// User-specific installation: write to HKCU
				log.debug("Writing double to user-specific preferences: {} = {}", key, value);
				Preferences userPrefs = getPartsKickPrefs(false);
				userPrefs.putDouble(key, value);
				return true;
			}
		} catch (Exception e) {
			log.error("Failed to write double preference {} = {}: {}", key, value, e.getMessage());
			return false;
		}
	}
	
	/**
	 * Attempts to write a long preference value with admin privilege detection.
	 * For system-wide installations, checks admin privileges and shows dialog if needed.
	 * 
	 * @param key the preference key
	 * @param value the preference value
	 * @param parent the parent component for dialogs
	 * @param bundle the resource bundle for localized messages
	 * @return true if write was successful or user chose to restart, false if user cancelled
	 */
	public static boolean putLongPreferenceWithAdminCheck(String key, long value, Component parent, ResourceBundle bundle) {
		try {
			boolean useSystemRoot = getInstallationType();
			
			if (useSystemRoot) {
				// System-wide installation: try HKLM first
				try {
					Preferences systemPrefs = getPartsKickPrefs(true);
					systemPrefs.putLong(key, value);
					log.debug("Successfully wrote long to system-wide preferences (HKLM): {} = {}", key, value);
					return true;
				} catch (SecurityException e) {
					log.warn("Failed to write long to system-wide preferences (HKLM) - admin privileges required: {} = {}", key, value);
					
					// Show dialog asking user to restart with admin privileges
					boolean restart = showAdminPrivilegeRequiredDialog(parent, bundle);
					if (restart) {
						log.info("User chose to restart with admin privileges");
						return true;
					} else {
						log.info("User cancelled - long preference not saved: {} = {}", key, value);
						return false;
					}
				}
			} else {
				// User-specific installation: write to HKCU
				log.debug("Writing long to user-specific preferences: {} = {}", key, value);
				Preferences userPrefs = getPartsKickPrefs(false);
				userPrefs.putLong(key, value);
				return true;
			}
		} catch (Exception e) {
			log.error("Failed to write long preference {} = {}: {}", key, value, e.getMessage());
			return false;
		}
	}
}
