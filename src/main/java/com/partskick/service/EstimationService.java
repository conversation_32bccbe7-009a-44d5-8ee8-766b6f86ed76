package com.partskick.service;

import com.linuxense.javadbf.DBFField;
import com.linuxense.javadbf.DBFReader;
import com.linuxense.javadbf.DBFWriter;
import com.partskick.Global;
import com.partskick.Utils;
import com.partskick.model.Estimation;
import com.partskick.parts.EmsInfo;
import com.partskick.parts.LineItemRecord;
import com.partskick.parts.PartsItemDto;
import com.partskick.parts.PartsTypeHelper;
import com.partskick.ui.EmsVehicleDto;
import com.partskick.ui.LineItem;
import com.partskick.ui.ProcessType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Slf4j
public class EstimationService {
    public static Pair<List<File>, Map<String, List<File>>> loadEMS(String path, int loadNumber, int loadPeriod, boolean silent) {
        return loadEMS(path, Global.getLoadNumber(loadNumber), Global.getLoadPeriod(loadPeriod), silent);
    }

    public static Pair<List<File>, Map<String, List<File>>> loadEMS(String path, int limit, long period, boolean silent) {
        long processStartAt = System.currentTimeMillis();
        String err;
        if (StringUtils.isBlank(path)) {
            err = "path is empty, Worker stopped!";
            if (!silent) log.error(err);
            throw new RuntimeException(err);
        } else {
            try {
                if (!silent) log.info("Processing files under {}", path);
                File dir = new File(path);
                if (dir == null) {
                    err = "Directory " + path + " does not exist.";
                    if (!silent) log.error(err);
                    throw new RuntimeException(err);
                }
                File[] files = dir.listFiles(f -> f.isFile() && (f.lastModified() > period) && StringUtils.isNotBlank(f.getName())
                        && (f.getName().toUpperCase().endsWith(".ENV")));
                if (files == null || files.length == 0) {
                    return null;
                }

                String message = "Total ENV files under " + path + " : " + files.length + " newer than " + new Date(period);
                if (!silent) {
                    log.info(message);
                } else if (Global.enableDebug) {
                    Utils.buildDebug(Global.PU_TASK, message);
                }

                Arrays.sort(files, Comparator.comparingLong(File::lastModified).reversed());

                if (files.length > limit) {
                    if (!silent)
                        log.info("Only first {} files will be processed, skip {}.", limit, files.length - limit);
                    files = Arrays.copyOf(files, limit);
                }

                Map<String, List<File>> fileMaps = new HashMap<>();
                for (File file : files) {
                    String fileName = file.getName().toUpperCase();
                    String pfileName = fileName.substring(0, fileName.length() - 4);
                    File[] fileList = dir.listFiles(f -> f.isFile() && StringUtils.isNotBlank(f.getName())
                            && f.getName().toUpperCase().startsWith(pfileName) && f.length() > 0);
                    fileMaps.put(pfileName, new ArrayList<>(Arrays.asList(fileList)));
                }

                if (files.length > 0) {
                    String logMessage = "###### " + path + " : Total vehicles - " + files.length + " loaded. Total time spent: " + (System.currentTimeMillis() - processStartAt) / 1000 + " seconds.";
                    if (!silent) {
                        log.info(logMessage);
                    } else if (Global.enableDebug) {
                        Utils.buildDebug(Global.PU_TASK, logMessage);
                    }
                    return Pair.of(new ArrayList<>(Arrays.asList(files)), fileMaps);
                } else {
                    if (!silent) log.info("No File match under {}", path);
                }
            } catch (Exception e) {
                if (!silent) log.error(e.getMessage(), e);
            }
        }
        return null;
    }

    public static Estimation processSingleEms(File file, Map<String, List<File>> fileMaps) {
        return processSingleEms(file, fileMaps, false);
    }

    public static Estimation processSingleEms(File file, Map<String, List<File>> fileMaps, boolean vehicleHeaderOnly) {
        String fileName = file.getName().toUpperCase();
        String pFileName = fileName.substring(0, fileName.length() - 4);

        log.debug("Unique File: {}", pFileName);
        String msgExpPre = "*** " + pFileName + " *** ";
        List<File> vfileList = fileMaps.get(pFileName);
        File vFile = file;
        Estimation estimation = new Estimation();
        try (FileInputStream is = new FileInputStream(vFile); DBFReader reader = new DBFReader(is)) {
            int numberOfFields = reader.getFieldCount();
            List<String> fields = new ArrayList<>();
            for (int i = 0; i < numberOfFields; i++) {
                DBFField field = reader.getField(i);
                fields.add(field.getName().toUpperCase());
            }
            Object[] rowObjects = reader.nextRecord();
            String vId = (String) rowObjects[fields.lastIndexOf("ESTFILE_ID")];
            if (StringUtils.isBlank(vId)) {
                log.error("{} has no unique vehicle estimation ID defined. Skip this file.", vFile.getName());
                return null;
            }
            String o = (String) rowObjects[fields.lastIndexOf("RO_ID")];
            if (StringUtils.isNotBlank(o) && !o.equals("0")) {
                estimation.setRoId(o);
            }
            estimation.setFileName(fileName);
            estimation.setFileDate(new Date(file.lastModified()));
        } catch (Exception ex) {
            log.error(msgExpPre + ex.getMessage(), ex);
        }

        for (File xFile : vfileList) {
            vFile = xFile;
            String _fileName = vFile.getName().toUpperCase();
            String _ext = _fileName.substring(_fileName.length() - 3);
            switch (_ext) {
                case "VEH":
                    try (FileInputStream is = new FileInputStream(vFile); DBFReader reader = new DBFReader(is)) {
                        int numberOfFields = reader.getFieldCount();
                        List<String> fields = new ArrayList<>();
                        for (int i = 0; i < numberOfFields; i++) {
                            DBFField field = reader.getField(i);
                            fields.add(field.getName().toUpperCase());
                        }
                        Object[] rowObjects = reader.nextRecord();
                        estimation.setYear((String) rowObjects[fields.lastIndexOf("V_MODEL_YR")]);
                        String make = (String) rowObjects[fields.lastIndexOf("V_MAKEDESC")];
                        if (StringUtils.isBlank(make)) {
                            make = (String) rowObjects[fields.lastIndexOf("V_MAKECODE")];
                        }
                        estimation.setMake(StringUtils.isNotBlank(make) ? make : "UNDEFINED");
                        estimation.setModel((String) rowObjects[fields.lastIndexOf("V_MODEL")]);
                        estimation.setVin((String) rowObjects[fields.lastIndexOf("V_VIN")]);
                    } catch (Exception ex) {
                        log.error(msgExpPre + ex.getMessage(), ex);
                    }
                    break;
                case "AD1":
                    if (vehicleHeaderOnly) {
                        break;
                    }
                    try (FileInputStream is = new FileInputStream(vFile); DBFReader reader = new DBFReader(is)) {
                        int numberOfFields = reader.getFieldCount();
                        List<String> fields = new ArrayList<>();
                        for (int i = 0; i < numberOfFields; i++) {
                            DBFField field = reader.getField(i);
                            fields.add(field.getName().toUpperCase());
                        }
                        Object[] rowObjects = reader.nextRecord();
                        Object o = rowObjects[fields.lastIndexOf("INSD_FN")];
                        if (null != o) {
                            estimation.setFirstName(o.toString());
                        }
                        o = rowObjects[fields.lastIndexOf("INSD_LN")];
                        if (null != o) {
                            estimation.setLastName(o.toString());
                        }
                        o = rowObjects[fields.lastIndexOf("INS_CO_NM")];
                        if (null != o) {
                            estimation.setInsuranceCompany(o.toString());
                        }
                        o = rowObjects[fields.lastIndexOf("OWNR_FN")];
                        if (null != o) {
                            estimation.setOwnerFirstName(o.toString());
                        }
                        o = rowObjects[fields.lastIndexOf("OWNR_LN")];
                        if (null != o) {
                            estimation.setOwnerLastName(o.toString());
                        }
                        o = rowObjects[fields.lastIndexOf("CLM_NO")];
                        if (null != o) {
                            estimation.setClaimNum(o.toString());
                        }
                        o = rowObjects[fields.lastIndexOf("CLM_CITY")];
                        if (null != o) {
                            estimation.setClaimCity(o.toString());
                        }
                        o = rowObjects[fields.lastIndexOf("CLM_CTRY")];
                        if (null != o) {
                            estimation.setClaimCountry(o.toString());
                        }

                    } catch (Exception ex) {
                        log.error(msgExpPre + ex.getMessage(), ex);
                    }
                    break;
            }
        }
        return estimation;
    }

    public static Pair<List<LineItem>, String> loadLineItem(String path, Estimation estimation, String ext, String emsExt) {
        return loadLineItem(path, estimation, ext, emsExt, false);
    }

    public static Pair<List<LineItem>, String> loadLineItem(String path, Estimation estimation, String ext, String emsExt, boolean bs) {
        return loadLineItem(path, estimation, ext, emsExt, bs, false);
    }

    public static Pair<List<LineItem>, String> loadLineItem(String path, Estimation estimation, String ext, String emsExt, boolean bs, boolean allowDuplicateUpload) {
        String fileName = estimation.getFileName();
        String year = estimation.getYear();
        String make = estimation.getMake();
        String model = estimation.getModel();
        List<LineItem> items = new ArrayList<>();
        List<LineItemRecord> records = new ArrayList<>();
        Set<Integer> lineNums = new HashSet<>();
        String newRoId = "";
        if (StringUtils.isBlank(path) || StringUtils.isBlank(fileName) || StringUtils.isBlank(ext)) {
            if (!bs) log.error("Invalid parameters: path {}, fileName {}, ext {}", path, fileName, ext);
        } else {
            try {
                String ofn = fileName;
                List<PartsItemDto> parts = new ArrayList<>();
                if (!bs) log.info("Processing file {} under {}", fileName, path);
                File dir = new File(path);
                File[] files = dir.listFiles();
                if (files == null || files.length == 0) {
                    if (!bs) log.error("directory {} is empty!", path);
                    return Pair.of(items, newRoId);
                }
                fileName = fileName.substring(0, fileName.length() - 4);
                for (File file : files) {
                    if (file != null && file.isFile()) {
                        String fn = file.getName();
                        String pn = fn.substring(0, fn.length() - 4);
                        String extn = fn.substring(fn.length() - 3);
                        if (fileName.equalsIgnoreCase(pn) && ext.equalsIgnoreCase(extn)) {
                            try (FileInputStream is = new FileInputStream(file); DBFReader reader = new DBFReader(is)) {
                                int numberOfFields = reader.getFieldCount();
                                List<String> fields = new ArrayList<>();
                                for (int i = 0; i < numberOfFields; i++) {
                                    DBFField field = reader.getField(i);
                                    fields.add(field.getName().toUpperCase());
                                }
                                Object[] rowObjects;
                                while ((rowObjects = reader.nextRecord()) != null) {
                                    LineItemRecord record = new LineItemRecord();
                                    // Map DBF fields to record properties
                                    record.setLineNo(((BigDecimal) rowObjects[fields.lastIndexOf("LINE_NO")]).intValue());
                                    record.setLineDesc((String) rowObjects[fields.lastIndexOf("LINE_DESC")]);
                                    record.setPartType((String) rowObjects[fields.lastIndexOf("PART_TYPE")]);
                                    record.setGlassFlag((Boolean) rowObjects[fields.lastIndexOf("GLASS_FLAG")]);
                                    record.setOemPartno((String) rowObjects[fields.lastIndexOf("OEM_PARTNO")]);
                                    record.setAltPartno((String) rowObjects[fields.lastIndexOf("ALT_PARTNO")]);
                                    record.setActPrice((BigDecimal) rowObjects[fields.lastIndexOf("ACT_PRICE")]);
                                    record.setPartQty(((BigDecimal) rowObjects[fields.lastIndexOf("PART_QTY")]).intValue());

                                    // Optional fields - check if they exist in the DBF
                                    if (fields.contains("LINE_IND"))
                                        record.setLineInd((String) rowObjects[fields.lastIndexOf("LINE_IND")]);
                                    if (fields.contains("LINE_REF"))
                                        record.setLineRef(((BigDecimal) rowObjects[fields.lastIndexOf("LINE_REF")]).intValue());
                                    if (fields.contains("TRAN_CODE"))
                                        record.setTranCode((String) rowObjects[fields.lastIndexOf("TRAN_CODE")]);
                                    if (fields.contains("DB_REF"))
                                        record.setDbRef((String) rowObjects[fields.lastIndexOf("DB_REF")]);
                                    if (fields.contains("UNQ_SEQ"))
                                        record.setUnqSeq(((BigDecimal) rowObjects[fields.lastIndexOf("UNQ_SEQ")]).intValue());
                                    if (fields.contains("WHO_PAYS"))
                                        record.setWhoPays((String) rowObjects[fields.lastIndexOf("WHO_PAYS")]);
                                    if (fields.contains("MOD_LBR_TY"))
                                        record.setModLbrTy((String) rowObjects[fields.lastIndexOf("MOD_LBR_TY")]);
                                    if (fields.contains("DB_HRS"))
                                        record.setDbHrs((BigDecimal) rowObjects[fields.lastIndexOf("DB_HRS")]);
                                    if (fields.contains("MOD_LB_HRS"))
                                        record.setModLbHrs((BigDecimal) rowObjects[fields.lastIndexOf("MOD_LB_HRS")]);
                                    if (fields.contains("LBR_OP"))
                                        record.setLbrOp((String) rowObjects[fields.lastIndexOf("LBR_OP")]);

                                    // Trim string fields
                                    if (record.getLineInd() != null) {
                                        record.setLineInd(record.getLineInd().trim());
                                    }
                                    if (record.getTranCode() != null) {
                                        record.setTranCode(record.getTranCode().trim());
                                    }
                                    if (record.getDbRef() != null) {
                                        record.setDbRef(record.getDbRef().trim());
                                    }
                                    if (record.getWhoPays() != null) {
                                        record.setWhoPays(record.getWhoPays().trim());
                                    }
                                    if (record.getLineDesc() != null) {
                                        record.setLineDesc(record.getLineDesc().trim());
                                    }
                                    if (record.getPartType() != null) {
                                        record.setPartType(record.getPartType().trim());
                                    }
                                    if (record.getOemPartno() != null) {
                                        record.setOemPartno(record.getOemPartno().trim());
                                    }
                                    if (record.getAltCoId() != null) {
                                        record.setAltCoId(record.getAltCoId().trim());
                                    }
                                    if (record.getAltPartno() != null) {
                                        record.setAltPartno(record.getAltPartno().trim());
                                    }
                                    if (record.getAltPartm() != null) {
                                        record.setAltPartm(record.getAltPartm().trim());
                                    }
                                    if (record.getModLbrTy() != null) {
                                        record.setModLbrTy(record.getModLbrTy().trim());
                                    }
                                    if (record.getLbrOp() != null) {
                                        record.setLbrOp(record.getLbrOp().trim());
                                    }
                                    if (record.getBettType() != null) {
                                        record.setBettType(record.getBettType().trim());
                                    }
                                    records.add(record);

                                    LineItem item = new LineItem();

                                    Integer lineNum = ((BigDecimal) rowObjects[fields.lastIndexOf("LINE_NO")]).intValue();
                                    if (lineNums.contains(lineNum)) {
                                        continue;
                                    } else {
                                        lineNums.add(lineNum);
                                    }
                                    item.setLineNum(lineNum);
                                    if (rowObjects[fields.lastIndexOf("LINE_IND")] != null) {
                                        item.setLineInd((String) rowObjects[fields.lastIndexOf("LINE_IND")]);
                                    }
                                    item.setQty(((BigDecimal) rowObjects[fields.lastIndexOf("PART_QTY")]).intValue());
                                    item.setPartsType(PartsTypeHelper.getByType((String) rowObjects[fields.lastIndexOf("PART_TYPE")]));
                                    item.setPartsTypeName((String) rowObjects[fields.lastIndexOf("PART_TYPE")]);
                                    String desc = (String) rowObjects[fields.lastIndexOf("LINE_DESC")];
                                    item.setDescription(desc);
                                    item.setPartsNum((String) rowObjects[fields.lastIndexOf("OEM_PARTNO")]);
                                    item.setAltPartsNum((String) rowObjects[fields.lastIndexOf("ALT_PARTNO")]);
                                    item.setIsGlass((Boolean) rowObjects[fields.lastIndexOf("GLASS_FLAG")]);
                                    item.setAmount(((BigDecimal) rowObjects[fields.lastIndexOf("ACT_PRICE")]).doubleValue());
                                    items.add(item);
                                    if (StringUtils.isNotBlank(item.getPartsNum()) && !com.partskick.common.Global.EXCLUDE_PARTS.contains(item.getPartsNum().trim().toUpperCase())) {
                                        String xpn = item.getPartsNum().trim();
                                        String alt = StringUtils.isNotBlank(item.getAltPartsNum()) ? item.getAltPartsNum().trim() : "";
                                        parts.add(new PartsItemDto().setLineNumber(item.getLineNum())
                                                .setDesc(com.partskick.common.Utils.cleanDesc(item.getDescription()))
                                                .setPartsNum(xpn)
                                                .setAltPartsNum(xpn.equalsIgnoreCase(alt) ? "" : (!com.partskick.common.Global.EXCLUDE_PARTS.contains(alt.toUpperCase()) ? alt : ""))
                                                .setType(item.getPartsTypeName())
                                                .setPrice(item.getAmount()));
                                    }
                                }
                            } catch (Exception ex) {
                                if (!bs) log.error(ex.getMessage(), ex);
                            }
                        } else if (fileName.equalsIgnoreCase(pn) && emsExt.equalsIgnoreCase(extn)) {
                            try (FileInputStream is = new FileInputStream(file); DBFReader reader = new DBFReader(is)) {
                                int numberOfFields = reader.getFieldCount();
                                List<String> fields = new ArrayList<>();
                                for (int i = 0; i < numberOfFields; i++) {
                                    DBFField field = reader.getField(i);
                                    fields.add(field.getName().toUpperCase());
                                }
                                Object[] rowObjects;
                                while ((rowObjects = reader.nextRecord()) != null) {
                                    String o = (String) rowObjects[fields.lastIndexOf("RO_ID")];
                                    if (StringUtils.isNotBlank(o) && !o.equals("0")) {
                                        newRoId = o;
                                    }
                                    o = (String) rowObjects[fields.lastIndexOf("UNQFILE_ID")];
                                    if (StringUtils.isNotBlank(o)) {
                                        estimation.setFileId(o);
                                    } else {
                                        o = (String) rowObjects[fields.lastIndexOf("ESTFILE_ID")];
                                        if (StringUtils.isNotBlank(o)) {
                                            estimation.setFileId(o);
                                        }
                                    }
                                }
                            } catch (Exception ex) {
                                if (!bs) log.error(ex.getMessage(), ex);
                            }
                        }
                    }
                }
                if (!parts.isEmpty()) {
                    try {
                        LocalDateTime dt = estimation.getFileDate().toInstant().plusMillis(500).truncatedTo(ChronoUnit.SECONDS).atZone(ZoneId.of("UTC")).toLocalDateTime();
                        EmsVehicleDto emsVehicleDto = new EmsVehicleDto().setFileName(estimation.getFileName()).setYear(year.trim()).setMake(make.trim())
                                .setModel(model.trim()).setPartsItemList(parts).setEmsDate(dt);

                        EmsInfo info = new EmsInfo().setFileId(estimation.getFileId()).setVin(estimation.getVin()).setInsurance(estimation.getInsuranceCompany())
                                .setClaimNo(estimation.getClaimNum()).setClaimCity(estimation.getClaimCity()).setClaimCountry(estimation.getClaimCountry())
                                .setLineItems(records).setFileDate(dt);
                        emsVehicleDto.setFromTask(bs);
                        Global.uploaded.put(path + ofn, emsVehicleDto);
                        Global.fullUpload.put(path + ofn, info);
                    } catch (Exception e) {
                        if (!bs) log.error(e.getMessage(), e);
                    }
                }
            } catch (Exception e) {
                if (!bs) log.error(e.getMessage(), e);
            }
        }
        return Pair.of(items, newRoId);
    }

    public static boolean pushEMS(String pathFrom, List<String> targets, Estimation estimation, boolean passthrough, Map<Integer, LineItem> changedItem) {
        String fixEnv = Global.fixMtoCType;

        String fileName = estimation.getFileName();
        if (StringUtils.isBlank(pathFrom) || targets.isEmpty() || StringUtils.isBlank(fileName)) {
            log.error("invalid parameter: pathFrom {}, targets {}, file {}", pathFrom, String.join(" ## ", targets), fileName);
            return false;
        } else {
            try {
                fileName = fileName.substring(0, fileName.length() - 4);
                log.info("push files from {} to {} for EMS {}", pathFrom, String.join(" ## ", targets), fileName);
                File dir = new File(pathFrom);
                File[] files = dir.listFiles();
                if (files == null || files.length == 0) {
                    log.error("{} is empty", pathFrom);
                    return false;
                }
                Set<Integer> lineNums = new HashSet<>();
                for (File file : files) {
                    if (file != null && file.isFile()) {
                        String fn = file.getName();
                        String pfileName = fn.substring(0, fn.length() - 4);
                        if (pfileName.toUpperCase().startsWith(fileName.toUpperCase())) {
                            String ext = fn.substring(fn.length() - 3);
                            if (!passthrough && ext.equalsIgnoreCase("LIN")) {
                                // Process LIN file locally first, then copy to targets
                                File tempFile = processLinFileLocally(file, pfileName, lineNums, changedItem);
                                if (tempFile != null) {
                                    try {
                                        // Copy the processed file to all targets
                                        boolean copySuccess = copyFileToTargets(tempFile, targets, pfileName, ".LIN");
                                        if (!copySuccess) {
                                            log.error("Failed to copy processed LIN file to targets");
                                            return false;
                                        }
                                    } finally {
                                        // Clean up temporary file
                                        if (!tempFile.delete()) {
                                            log.warn("Failed to delete temporary file: {}", tempFile.getAbsolutePath());
                                        }
                                    }
                                } else {
                                    log.error("Failed to process LIN file locally");
                                    return false;
                                }
                            } else if (ext.equalsIgnoreCase("ENV")) {
                                log.info("RO: {} , NewRO: {}", estimation.getRoId(), estimation.getNewRoId());
                                // Process ENV file locally first, then copy to targets
                                File tempFile = processEnvFileLocally(file, pfileName, estimation, fixEnv);
                                if (tempFile != null) {
                                    try {
                                        // Copy the processed file to all targets
                                        boolean copySuccess = copyFileToTargets(tempFile, targets, pfileName, ".ENV");
                                        if (!copySuccess) {
                                            log.error("Failed to copy processed ENV file to targets");
                                            return false;
                                        }
                                    } finally {
                                        // Clean up temporary file
                                        if (!tempFile.delete()) {
                                            log.warn("Failed to delete temporary file: {}", tempFile.getAbsolutePath());
                                        }
                                    }
                                } else {
                                    log.error("Failed to process ENV file locally");
                                    return false;
                                }
/*
                            } else if (ext.equalsIgnoreCase("AD1") && ClientHelper.isIfRemovePrivacy()) {
                                // Process AD1 file locally first, then copy to targets
                                File tempFile = removeFieldLocally(file, false, List.of("INS_CO_NM"), pfileName, ".AD1");
                                if (tempFile != null) {
                                    try {
                                        boolean copySuccess = copyFileToTargets(tempFile, targets, pfileName, ".AD1");
                                        if (!copySuccess) {
                                            log.error("Failed to copy processed AD1 file to targets");
                                            return false;
                                        }
                                    } finally {
                                        if (!tempFile.delete()) {
                                            log.warn("Failed to delete temporary file: {}", tempFile.getAbsolutePath());
                                        }
                                    }
                                } else {
                                    log.error("Failed to process AD1 file locally");
                                    return false;
                                }
                            } else if (ext.equalsIgnoreCase("AD2") && ClientHelper.isIfRemovePrivacy()) {
                                // Process AD2 file locally first, then copy to targets
                                File tempFile = removeFieldLocally(file, true, null, pfileName, ".AD2");
                                if (tempFile != null) {
                                    try {
                                        boolean copySuccess = copyFileToTargets(tempFile, targets, pfileName, ".AD2");
                                        if (!copySuccess) {
                                            log.error("Failed to copy processed AD2 file to targets");
                                            return false;
                                        }
                                    } finally {
                                        if (!tempFile.delete()) {
                                            log.warn("Failed to delete temporary file: {}", tempFile.getAbsolutePath());
                                        }
                                    }
                                } else {
                                    log.error("Failed to process AD2 file locally");
                                    return false;
                                }
*/
                            } else {
                                // For files that don't need processing, copy directly to targets
                                for (String pathTo : targets) {
                                    Path fp = Paths.get(pathTo, file.getName());
                                    Files.copy(Paths.get(file.getAbsolutePath()), fp, StandardCopyOption.REPLACE_EXISTING);
                                    File fx = fp.toFile();
                                    fx.setLastModified(System.currentTimeMillis());
                                }
                            }
                        }
                    }

                }
                return true;
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return false;
    }

    private static File processLinFileLocally(File file, String pfileName, Set<Integer> lineNums, Map<Integer, LineItem> changedItem) {
        try (FileInputStream is = new FileInputStream(file); DBFReader reader = new DBFReader(is)) {
            int numberOfFields = reader.getFieldCount();
            DBFField[] fields = new DBFField[numberOfFields];
            List<String> fieldNames = new ArrayList<>();
            List<Object[]> cellObjects = new ArrayList<>();
            for (int i = 0; i < numberOfFields; i++) {
                DBFField field = reader.getField(i);
                fields[i] = field;
                fieldNames.add(field.getName().toUpperCase());
            }
            Object[] rowObjects;
            while ((rowObjects = reader.nextRecord()) != null) {
                int lineNum = ((BigDecimal) (rowObjects[fieldNames.lastIndexOf("LINE_NO")])).intValue();
                if (lineNums.contains(lineNum)) {
                    continue;
                } else {
                    lineNums.add(lineNum);
                }
                if (changedItem.containsKey(lineNum)) {
                    LineItem lineItem = changedItem.get(lineNum);
                    if (lineItem.getAction().equals(ProcessType.Include)) {
                        rowObjects[fieldNames.lastIndexOf("PART_TYPE")] = lineItem.getPartsType().getType();
                        rowObjects[fieldNames.lastIndexOf("LINE_DESC")] = lineItem.getDescription();
                        rowObjects[fieldNames.lastIndexOf("OEM_PARTNO")] = lineItem.getPartsNum();
                        rowObjects[fieldNames.lastIndexOf("ALT_PARTNO")] = lineItem.getAltPartsNum();
                        rowObjects[fieldNames.lastIndexOf("ACT_PRICE")] = lineItem.getAmtAfterDisc();
                    } else if (lineItem.getAction().equals(ProcessType.Not_To_Order)) {
                        rowObjects[fieldNames.lastIndexOf("PART_TYPE")] = PartsTypeHelper.getMarkRemovedType();
                    }
                }
                cellObjects.add(rowObjects);
            }
            
            // Create temporary file locally
            return writeToLocalTempFile(pfileName, fields, cellObjects, ".LIN");
        } catch (Exception ex) {
            log.error("Failed to process LIN file locally: {}", ex.getMessage(), ex);
            return null;
        }
    }

    private static File processEnvFileLocally(File file, String pfileName, Estimation estimation, String fixEnv) {
        try (FileInputStream is = new FileInputStream(file); DBFReader reader = new DBFReader(is)) {
            int numberOfFields = reader.getFieldCount();
            DBFField[] fields = new DBFField[numberOfFields];
            List<String> fieldNames = new ArrayList<>();
            List<Object[]> cellObjects = new ArrayList<>();
            for (int i = 0; i < numberOfFields; i++) {
                DBFField field = reader.getField(i);
                fields[i] = field;
                fieldNames.add(field.getName().toUpperCase());
            }
            Object[] rowObjects;
            while ((rowObjects = reader.nextRecord()) != null) {
                rowObjects[fieldNames.lastIndexOf("RO_ID")] = estimation.getNewRoId();
                String estSystem = String.valueOf(rowObjects[fieldNames.lastIndexOf("EST_SYSTEM")]);
                if (Global.fixMtoC && StringUtils.isNotBlank(fixEnv) && !fixEnv.equals("0") && StringUtils.isNotBlank(estSystem) && estSystem.equalsIgnoreCase("M")) {
                    switch (fixEnv) {
                        case "1":
                            rowObjects[fieldNames.lastIndexOf("EST_SYSTEM")] = "C";
                            break;
                        case "2":
                            String uniqueId2 = String.valueOf(rowObjects[fieldNames.lastIndexOf("UNQFILE_ID")]);
                            if (StringUtils.isBlank(uniqueId2)) {
                                rowObjects[fieldNames.lastIndexOf("UNQFILE_ID")] = rowObjects[fieldNames.lastIndexOf("ESTFILE_ID")];
                            }
                            break;
                        case "3":
                            rowObjects[fieldNames.lastIndexOf("EST_SYSTEM")] = "C";
                            String uniqueId3 = String.valueOf(rowObjects[fieldNames.lastIndexOf("UNQFILE_ID")]);
                            if (StringUtils.isBlank(uniqueId3)) {
                                rowObjects[fieldNames.lastIndexOf("UNQFILE_ID")] = rowObjects[fieldNames.lastIndexOf("ESTFILE_ID")];
                            }
                            break;
                        case "4":
                            rowObjects[fieldNames.lastIndexOf("ESTFILE_ID")] = estimation.getFileName();
                            break;
                        case "5":
                            rowObjects[fieldNames.lastIndexOf("EST_SYSTEM")] = "C";
                            rowObjects[fieldNames.lastIndexOf("ESTFILE_ID")] = estimation.getFileName();
                            break;
                        case "6":
                            rowObjects[fieldNames.lastIndexOf("EST_SYSTEM")] = "C";
                            rowObjects[fieldNames.lastIndexOf("ESTFILE_ID")] = estimation.getFileName();
                            rowObjects[fieldNames.lastIndexOf("UNQFILE_ID")] = estimation.getFileName();
                            break;
                    }
                }
                cellObjects.add(rowObjects);
            }
            
            // Create temporary file locally
            return writeToLocalTempFile(pfileName, fields, cellObjects, ".ENV");
        } catch (Exception ex) {
            log.error("Failed to process ENV file locally: {}", ex.getMessage(), ex);
            return null;
        }
    }

    private static File writeToLocalTempFile(String pfileName, DBFField[] fields, List<Object[]> cellObjects, String ext) {
        if (!ext.startsWith(".")) ext = "." + ext;
        try {
            // Create temporary file in system temp directory
            File tempFile = File.createTempFile(pfileName + "_", ext);
            tempFile.deleteOnExit(); // Ensure cleanup on JVM exit
            
            try (DBFWriter writer = new DBFWriter(new FileOutputStream(tempFile))) {
                writer.setFields(fields);
                cellObjects.forEach(writer::addRecord);
            }
            
            // Set last modified time
            tempFile.setLastModified(System.currentTimeMillis());
            
            log.debug("Created temporary file: {}", tempFile.getAbsolutePath());
            return tempFile;
        } catch (Exception ex) {
            log.error("Failed to create temporary file: {}", ex.getMessage(), ex);
            return null;
        }
    }

    private static boolean copyFileToTargets(File tempFile, List<String> targets, String pfileName, String ext) {
        for (String pathTo : targets) {
            try {
                Path fp = Paths.get(pathTo, tempFile.getName());
                Files.copy(Paths.get(tempFile.getAbsolutePath()), fp, StandardCopyOption.REPLACE_EXISTING);
                File fx = fp.toFile();
                fx.setLastModified(System.currentTimeMillis());
            } catch (Exception ex) {
                log.error("Failed to copy file {} to {}: {}", tempFile.getName(), pathTo, ex.getMessage(), ex);
                return false;
            }
        }
        return true;
    }

    public static boolean pushEMSWithoutChange(String pathFrom, String pathTo, Estimation estimation) {
        String fileName = estimation.getFileName();
        if (StringUtils.isBlank(pathFrom) || StringUtils.isBlank(pathTo) || StringUtils.isBlank(fileName)) {
            log.error("invalid parameter: pathFrom {}, pathTo {}, file {}", pathFrom, pathTo, fileName);
            return false;
        } else {
            try {
                fileName = fileName.substring(0, fileName.length() - 4);
                log.info("push files from {} to {} for EMS {}", pathFrom, pathTo, fileName);
                File dir = new File(pathFrom);
                File[] files = dir.listFiles();
                if (files == null || files.length == 0) {
                    log.error("{} is empty", pathFrom);
                    return false;
                }

                for (File file : files) {
                    if (file != null && file.isFile()) {
                        String fn = file.getName();
                        String pfileName = fn.substring(0, fn.length() - 4);
                        if (pfileName.toUpperCase().startsWith(fileName.toUpperCase())) {
                            String ext = fn.substring(fn.length() - 3);
                            if (ext.equalsIgnoreCase("ENV") && !Objects.equals(estimation.getRoId(), estimation.getNewRoId())) {
                                log.info("RO: {} , NewRO: {}", estimation.getRoId(), estimation.getNewRoId());
                                // Process ENV file locally first, then copy to target
                                File tempFile = processEnvFileWithoutChangeLocally(file, pfileName, estimation);
                                if (tempFile != null) {
                                    try {
                                        // Copy the processed file to target
                                        Path fp = Paths.get(pathTo, tempFile.getName());
                                        Files.copy(Paths.get(tempFile.getAbsolutePath()), fp, StandardCopyOption.REPLACE_EXISTING);
                                        File fx = fp.toFile();
                                        fx.setLastModified(System.currentTimeMillis());
                                    } finally {
                                        // Clean up temporary file
                                        if (!tempFile.delete()) {
                                            log.warn("Failed to delete temporary file: {}", tempFile.getAbsolutePath());
                                        }
                                    }
                                } else {
                                    log.error("Failed to process ENV file locally");
                                    return false;
                                }
                            } else {
                                // copy file directly
                                Files.copy(Paths.get(file.getAbsolutePath()), Paths.get(pathTo, file.getName()), StandardCopyOption.REPLACE_EXISTING);
                            }
                        }
                    }

                }
                return true;
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return false;
    }

    private static File processEnvFileWithoutChangeLocally(File file, String pfileName, Estimation estimation) {
        try (FileInputStream is = new FileInputStream(file); DBFReader reader = new DBFReader(is)) {
            int numberOfFields = reader.getFieldCount();
            DBFField[] fields = new DBFField[numberOfFields];
            List<String> fieldNames = new ArrayList<>();
            List<Object[]> cellObjects = new ArrayList<>();
            for (int i = 0; i < numberOfFields; i++) {
                DBFField field = reader.getField(i);
                fields[i] = field;
                fieldNames.add(field.getName().toUpperCase());
            }
            Object[] rowObjects;
            while ((rowObjects = reader.nextRecord()) != null) {
                rowObjects[fieldNames.lastIndexOf("RO_ID")] = estimation.getNewRoId();
                cellObjects.add(rowObjects);
            }
            
            // Create temporary file locally
            return writeToLocalTempFile(pfileName, fields, cellObjects, ".ENV");
        } catch (Exception ex) {
            log.error("Failed to process ENV file locally: {}", ex.getMessage(), ex);
            return null;
        }
    }

    private static File removeFieldLocally(File file, boolean removeAll, List<String> fieldsList, String pfileName, String ext) {
        DBFField[] fields;
        List<Object[]> cellObjects = new ArrayList<>();
        // read from file
        try (FileInputStream is = new FileInputStream(file); DBFReader reader = new DBFReader(is)) {
            int numberOfFields = reader.getFieldCount();
            fields = new DBFField[numberOfFields];
            for (int i = 0; i < numberOfFields; i++) {
                DBFField field = reader.getField(i);
                fields[i] = field;
            }
            Object[] rowObjects;
            while ((rowObjects = reader.nextRecord()) != null) {
                for(int j = 0; j < rowObjects.length; j++) {
                    if (rowObjects[j] != null && StringUtils.isNotBlank(rowObjects[j].toString()) &&
                        (removeAll || fieldsList != null && (fieldsList.contains(fields[j].getName()) || fieldsList.contains(fields[j].getName().toUpperCase())))) {
                        rowObjects[j] = "***";
                    }
                }
                cellObjects.add(rowObjects);
            }

        } catch (Exception ex) {
            log.error("Failed to process file for field removal: {}", ex.getMessage(), ex);
            return null;
        }
        
        if (fields.length > 0 && !cellObjects.isEmpty()) {
            return writeToLocalTempFile(pfileName, fields, cellObjects, ext);
        }
        
        return null;
    }
}

