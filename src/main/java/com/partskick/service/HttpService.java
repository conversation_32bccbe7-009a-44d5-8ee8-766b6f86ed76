package com.partskick.service;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.databind.type.MapType;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.partskick.PkVersion;
import com.partskick.Utils;
import com.partskick.client.*;
import com.partskick.common.*;
import com.partskick.parts.*;
import com.partskick.ui.EmsSource;
import com.partskick.ui.EmsVehicleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class HttpService {
    private PropertyLoader propertyLoader;
    private AESEncryptionDecryption aesEncryptionDecryption = new AESEncryptionDecryption();

    public static ObjectMapper mapper = new ObjectMapper().registerModule(new JavaTimeModule());

    public HttpService() {
        propertyLoader = new PropertyLoader();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public Pair<Integer, String> getRegistrationStatus(String client, String station, String installKey, String machineId, Long submitTime) {
        try {
            String content = "{\"workstation\":\"" + station + "\",\"submitTime\":" + submitTime + ",\"installKey\":\"" + installKey + "\"}";
            HttpResponse<String> response = postStringHttpResponse(client, machineId, "register-with-key", "v4", content);
            return parseResponse(response, submitTime);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Pair.of(License.exception.getStatus(), e.getMessage());
        }
    }

    public Pair<Integer, String> validateStatus(String client, String station, String machineId, Long submitTime) {
        try {
            HttpResponse<String> response = postStringHttpResponse(client, station, machineId, submitTime, "validate");
            return parseResponse(response, submitTime);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Pair.of(License.exception.getStatus(), e.getMessage());
        }
    }

    public void audit(String client, String station, String machineId, String event, String subject) {
        try {
            HttpResponse<String> response = postAuditHttpResponse(client, station, machineId, "audit", event, subject);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private HttpResponse<String> postAuditHttpResponse(String client, String station, String machineId, String path, String event, String subject) throws URISyntaxException, IOException, InterruptedException {
        HttpRequest request = buildBaseRequest(client, machineId, path)
                .POST(HttpRequest.BodyPublishers.ofString("{\"workstation\":\""+ station +"\",\"event\":\""+ event +"\",\"subject\":\""+ subject +"\"}"))
                .build();
        return HttpClient.newHttpClient()
                .send(request, HttpResponse.BodyHandlers.ofString());
    }


    public List<PartsType> getPartsType(String clientId, String machineId) throws URISyntaxException, IOException, InterruptedException {
        HttpResponse<String> response = getStringHttpResponse(clientId, machineId, "parts-type");
        return mapper.readerForListOf(PartsType.class).readValue(response.body());
    }

    public List<PartsReplaceType> getPartsNumReplacement(String clientId, String machineId) throws URISyntaxException, IOException, InterruptedException {
        HttpResponse<String> response = getStringHttpResponse(clientId, machineId, "parts-replace");
        return mapper.readerForListOf(PartsReplaceType.class).readValue(response.body());
    }

    public List<EmsSource> getEmsSources(String clientId, String machineId) throws URISyntaxException, IOException, InterruptedException {
        HttpResponse<String> response = getStringHttpResponse(clientId, machineId, "ems-source");
        return mapper.readerForListOf(EmsSource.class).readValue(response.body());

    }

    public List<PartsTypeTieredDisc> getPartsTieredDiscount(String clientId, String machineId) throws URISyntaxException, IOException, InterruptedException {
        HttpResponse<String> response = getStringHttpResponse(clientId, machineId, "parts-discount");
        return mapper.readerForListOf(PartsTypeTieredDisc.class).readValue(response.body());
    }

    public List<PartsTypeExtraDisc> getPartsExtraDiscount(String clientId, String machineId) throws URISyntaxException, IOException, InterruptedException {
        HttpResponse<String> response = getStringHttpResponse(clientId, machineId, "parts-extra-discount");
        return mapper.readerForListOf(PartsTypeExtraDisc.class).readValue(response.body());
    }

    public PartsConfig getPartsConfig(String clientId, String machineId) throws URISyntaxException, IOException, InterruptedException {
        HttpResponse<String> response = getStringHttpResponse(clientId, machineId, "parts-configs", "v5");
        return mapper.readerFor(PartsConfig.class).readValue(response.body());
    }

    public InvoicePreferenceV2 getInvoicePreference(String clientId, String machineId) throws URISyntaxException, IOException, InterruptedException {
        HttpResponse<String> response = getStringHttpResponse(clientId, machineId, "invoice-preference-v2", "v5");
        if (response.statusCode() != 200) {
            return new InvoicePreferenceV2();
        }
        return mapper.readerFor(InvoicePreferenceV2.class).readValue(response.body());
    }

    public Long saveInvoicePreferenceClient(String clientId, String machineId, String ws, InvoicePreferenceClient preference) throws URISyntaxException, IOException, InterruptedException {
        preference.setClientId(clientId);
        HttpRequest request = buildBaseRequest(clientId, machineId, ws, "invoice-preference-client", "v5")
                .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(preference)))
                .build();
        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() != 200) {
            log.error("failed to update invoice preference");
            return null;
        }
        return Long.parseLong(response.body());
    }

    public Long saveSupplier(String clientId, String machineId, String ws, InvoicePreferenceSupplier supplier) throws URISyntaxException, IOException, InterruptedException {
        supplier.setClientId(clientId);
        HttpRequest request = buildBaseRequest(clientId, machineId, ws, "invoice-preference-supplier", "v5")
                .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(supplier)))
                .build();
        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() != 200) {
            log.error("failed to update supplier");
            return null;
        }
        return Long.parseLong(response.body());
    }

    public boolean deleteSupplier(String clientId, String machineId, String ws, Long supplierId) throws URISyntaxException, IOException, InterruptedException {
        HttpRequest request = buildBaseRequest(clientId, machineId, ws, "invoice-preference-supplier/" + supplierId, "v5")
                .DELETE()
                .build();
        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() != 200) {
            log.error("failed to delete supplier");
            return false;
        }
        return true;
    }

    public void messageOpenTrigger(String clientId, String machineId, String ws, MessageOpenRequest messageOpenRequest) throws URISyntaxException, IOException, InterruptedException {
        HttpRequest request = buildPortalBaseRequest(clientId, machineId, ws, "api/client/message/open")
                .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(messageOpenRequest)))
                .build();
        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() != 200) {
            log.error("failed to record message open");
        }
    }

    public void messageClickTrigger(String clientId, String machineId, String ws, MessageClickRequest messageClickRequest) throws URISyntaxException, IOException, InterruptedException {
        HttpRequest request = buildPortalBaseRequest(clientId, machineId, ws, "api/client/message/click")
                .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(messageClickRequest)))
                .build();
        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() != 200) {
            log.error("failed to record message click");
        }
    }

    public List<Tax> getTaxes(String clientId, String machineId) throws URISyntaxException, IOException, InterruptedException {
        HttpResponse<String> response = getStringHttpResponse(clientId, machineId, "tax-rate", "v4");
        if (response.statusCode() != 200) {
            log.error("failed to get taxes");
            return new ArrayList<>();
        }
        CollectionType javaType = mapper.getTypeFactory().constructCollectionType(List.class, Tax.class);
        return mapper.readerFor(javaType).readValue(response.body());
    }

    public NewInvoiceResponse getInvoiceHistory(String clientId, String machineId, InvoiceHistoryRequest invoiceHistoryRequest) throws URISyntaxException, IOException, InterruptedException {
        HttpRequest request = buildBaseRequest(clientId, machineId, "invoice-history", "v4")
                .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(invoiceHistoryRequest)))
                .build();
        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() != 200) {
            log.error("failed to get invoice history");
            return new NewInvoiceResponse().setInvoices(new ArrayList<>());
        }
        return mapper.readerFor(NewInvoiceResponse.class).readValue(response.body());
    }

    public List<Invoice> searchInvoice(String clientId, String machineId, InvoiceSearchRequest invoiceSearchRequest) throws URISyntaxException, IOException, InterruptedException {
        HttpRequest request = buildBaseRequest(clientId, machineId, "invoice-search", "v4")
                .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(invoiceSearchRequest)))
                .build();
        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() != 200) {
            log.error("failed to search invoice");
            throw new RuntimeException("failed to search invoice");
        }
        CollectionType javaType = mapper.getTypeFactory().constructCollectionType(List.class, Invoice.class);
        return mapper.readerFor(javaType).readValue(response.body());
    }

    public ClientV3 getClientInfoV3(String clientId, String machineId) throws URISyntaxException, IOException, InterruptedException {
        HttpResponse<String> response = getStringHttpResponse(clientId, machineId, "client-info", "v3");
        return mapper.readerFor(ClientV3.class).readValue(response.body());
    }
    public Client getClientInfo(String clientId, String machineId) throws URISyntaxException, IOException, InterruptedException {
        HttpResponse<String> response = getStringHttpResponse(clientId, machineId, "client-info");
        return mapper.readerFor(Client.class).readValue(response.body());
    }
    public Map<String, Object> getGlobalConfig(String clientId, String machineId) throws URISyntaxException, IOException, InterruptedException {
        HttpResponse<String> response = getStringHttpResponse(clientId, machineId, "globalConfig");
        return mapper.readerFor(Map.class).readValue(response.body());
    }
    public VerUpgrade getVersionUpgrade(String client, String ws) throws URISyntaxException, IOException, InterruptedException {
        return (VerUpgrade) singleValueRequest(client, ws,"version/" + PkVersion.version, VerUpgrade.class);
    }

    public Boolean ifRemovePrivacy(String client, String ws) throws URISyntaxException, IOException, InterruptedException {
        return (Boolean) singleValueRequest(client, ws, "pp", Boolean.class);
    }

    private Object singleValueRequest(String client, String ws, String path, Class resultType) throws URISyntaxException, IOException, InterruptedException {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(new URI(propertyLoader.getProperties().getProperty("pa.server.url") + "/v2/" + path))
                .header("Content-Type", "application/json")
                .header(Constants.clientHeader, client)
                .header(Constants.versionHeader, PkVersion.version.getValue())
//                .header(Constants.wsHeader, ws)
                .GET().build();
        HttpResponse<String> response = HttpClient.newHttpClient()
                .send(request,HttpResponse.BodyHandlers.ofString());
        return mapper.readerFor(resultType).readValue(response.body());

    }

    private HttpRequest.Builder buildBaseRequest(String client, String machineId, String path) throws URISyntaxException {
        return buildBaseRequest(client, machineId, path, "v2");
    }

    private HttpRequest.Builder buildBaseRequest(String client, String machineId, String path, String version) throws URISyntaxException {
        return buildBaseRequest(client, machineId, "", path, version);
    }

    private HttpRequest.Builder buildBaseRequest(String client, String machineId, String ws, String path, String version) throws URISyntaxException {
        return HttpRequest.newBuilder()
                .uri(new URI(propertyLoader.getProperties().getProperty("pa.server.url") + "/" + version + "/" + path))
                .header("Content-Type", "application/json")
                .header(Constants.clientHeader, client)
                .header(Constants.wsHeader, ws)
                .header(Constants.versionHeader, PkVersion.version.getValue())
                .header(Constants.signHeader, aesEncryptionDecryption.encrypt(machineId, Utils.encryptKey));
    }

    private HttpRequest.Builder buildMultipartRequest(String client, String machineId, String ws, String path, String version) throws URISyntaxException {
        return HttpRequest.newBuilder()
                .uri(new URI(propertyLoader.getProperties().getProperty("pa.server.url") + "/" + version + "/" + path))
                .header(Constants.clientHeader, client)
                .header(Constants.wsHeader, ws)
                .header(Constants.versionHeader, PkVersion.version.getValue())
                .header(Constants.signHeader, aesEncryptionDecryption.encrypt(machineId, Utils.encryptKey));
    }

    private HttpRequest.Builder buildPortalBaseRequest(String client, String machineId, String ws, String path) throws URISyntaxException {
        return HttpRequest.newBuilder()
                .uri(new URI(propertyLoader.getProperties().getProperty("portal.api.url") + "/" + path))
                .header("Content-Type", "application/json")
                .header(Constants.clientHeader, client)
                .header(Constants.wsHeader, ws)
                .header(Constants.versionHeader, PkVersion.version.getValue())
                .header(Constants.signHeader, aesEncryptionDecryption.encrypt(machineId, Utils.encryptKey));
    }

    private HttpResponse<String> postStringHttpResponse(String client, String station, String machineId, Long submitTime, String path) throws URISyntaxException, IOException, InterruptedException {
        return postStringHttpResponse(client, station, machineId, submitTime, path, "v2");
    }

    private HttpResponse<String> postStringHttpResponse(String client, String station, String machineId, Long submitTime, String path, String version) throws URISyntaxException, IOException, InterruptedException {
        HttpRequest request = buildBaseRequest(client, machineId, path, version)
                .POST(HttpRequest.BodyPublishers.ofString("{\"workstation\":\"" + station + "\",\"submitTime\":" + submitTime + "}"))
                .build();
        return HttpClient.newHttpClient()
                .send(request, HttpResponse.BodyHandlers.ofString());
    }

    private HttpResponse<String> postStringHttpResponse(String client, String machineId, String path, String version, String content) throws URISyntaxException, IOException, InterruptedException {
        HttpRequest request = buildBaseRequest(client, machineId, path, version)
                .POST(HttpRequest.BodyPublishers.ofString(content))
                .build();
        return HttpClient.newHttpClient()
                .send(request, HttpResponse.BodyHandlers.ofString());
    }

    private HttpResponse<String> getStringHttpResponse(String client, String machineId, String path) throws URISyntaxException, IOException, InterruptedException {
        return getStringHttpResponse(client, machineId, path, "v2");
    }
    private HttpResponse<String> getStringHttpResponse(String client, String machineId, String path, String version) throws URISyntaxException, IOException, InterruptedException {
        HttpRequest request = buildBaseRequest(client, machineId, path, version)
                .GET().build();
        return HttpClient.newHttpClient()
                .send(request,HttpResponse.BodyHandlers.ofString());
    }

    private Pair<Integer, String> parseResponse(HttpResponse<String> response, Long submitTime) {
        if (response.statusCode() == 200) {
            String result = response.body();
            String[] sp = result.split("====");
            int code = com.partskick.common.Utils.decodeStatus(sp[0], submitTime);
            return Pair.of(code, sp.length > 1 ? sp[1] : "");
        } else if (response.statusCode() == 201) {
            return Pair.of(License.wsCreated.getStatus(), response.body());
        } else if (response.statusCode() == 401) {
            return Pair.of(License.noSubscription.getStatus(), response.body());
        } else if (response.statusCode() == 400) {
            return Pair.of(License.error.getStatus(), response.body());
        } else if (response.statusCode() >= 500) {
            return Pair.of(License.exception.getStatus(), response.body());
        }
        return Pair.of(License.unknow.getStatus(), response.body());
    }

    public EmsPartsMatchDto uploadParts(String client, String machineId, String workstation, EmsVehicleDto parts) throws Exception {
        if (ClientHelper.getClient() instanceof ClientV3 clientV3 && clientV3.getPartsSearchScope() >= 0) {
            parts.setSearchScope(clientV3.getPartsSearchScope());
        }

        if (parts != null && parts.getPartsItemList() != null && !parts.getPartsItemList().isEmpty()) {
            parts.getPartsItemList().forEach(p -> {
                p.setDesc(com.partskick.common.Utils.cleanDesc(p.getDesc()));
                p.setPartsNum(com.partskick.common.Utils.removeChars(p.getPartsNum(), Constants.REMOVE_FROM_PARTS_NUMBER));
                p.setAltPartsNum(com.partskick.common.Utils.removeChars(p.getAltPartsNum(), Constants.REMOVE_FROM_ALT_PARTS_NUMBER));
            });
        }

        HttpRequest request = buildBaseRequest(client, machineId, workstation, "collect-parts", "v5")
                .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(parts)))
                .build();
        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() != 200) {
            log.error("parts search request failed with code {}, reason: {}", response.statusCode(), response.body());
            log.error("failed to upload parts: {} {} {}", parts.getYear(), parts.getMake(), parts.getModel());
        }
        return response.statusCode() == 200 ? mapper.readValue(response.body(), EmsPartsMatchDto.class) : null;
    }

    public void fullUpload(String client, String machineId, String workstation, EmsInfo info) throws Exception {
        HttpRequest request = buildBaseRequest(client, machineId, workstation, "full-upload", "v5")
                .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(info)))
                .build();
//        log.info("full upload sending request");
        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        log.info("full upload: {} {}, result: {}", info.getVin(), info.getFileId(), response.body());
    }

    public IssueInvoiceResponse issueInvoice(String client, String machineId, String workstation, IssueInvoiceRequest invoiceRequest) throws Exception {
        HttpRequest request = buildBaseRequest(client, machineId, workstation, "issue-invoice", "v4")
                .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(invoiceRequest)))
                .build();
        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() != 200) {
            log.error("failed to issue invoice");
            log.error("Request: {}", mapper.writeValueAsString(invoiceRequest));
        }
        return response.statusCode() == 200 ? mapper.readValue(response.body(), IssueInvoiceResponse.class) : new IssueInvoiceResponse().setSuccess(false).setMessage(response.body());
    }

    public void updatePartsPref(String client, String machineId, String workstation, List<Integer> keepType) throws Exception {
        HttpRequest request = buildBaseRequest(client, machineId, workstation, "parts-pref-keep-type", "v3")
                .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(keepType)))
                .build();
        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() != 200) {
            log.error("keep parts type update failed with code {}, reason: {}", response.statusCode(), response.body());
        }
    }

    public PartsParameters getPUParameters(String client, String machineId) throws Exception {
        HttpRequest request = buildBaseRequest(client, machineId, "cp-parameters", "v3")
                .GET().build();
        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() != 200) {
            log.error("failed to get collect parameters");
        }
        return response.statusCode() == 200 ? mapper.readValue(response.body(), PartsParameters.class) : null;
    }

    public void reportPUError(String client, String machineId, String workstation, String path, Long lastTime) throws Exception {
        HttpRequest request = buildBaseRequest(client, machineId, "cp-error", "v3")
                .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(new PkPnError().setWorkstation(workstation).setPath(path).setLastTime(lastTime)))
                ).build();
        HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
    }

    public PkPnDto getPkPn(String client, String machineId, PkPnDto parts) throws Exception {
        HttpRequest request = buildBaseRequest(client, machineId, "pk-pn", "v3")
                .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(parts)))
                .build();
        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() != 200) {
            log.error("failed to generate mapping id for parts number: {}", parts.getPn());
        }
        return response.statusCode() == 200 ? mapper.readValue(response.body(), PkPnDto.class) : null;
    }

    public ClientConfig getConfig(String client, String machineId, String workstation) throws Exception {
        String sysDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        HttpRequest request = buildBaseRequest(client, machineId, workstation, "system-config", "v5")
                .header(Constants.clientDateHeader, sysDate)
                .GET().build();
        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() != 200) {
            log.error("failed to get system configuration");
        }
        return response.statusCode() == 200 ? mapper.readValue(response.body(), ClientConfig.class) : null;
    }

    // obsoleted
    public PartsMatchDto getMatchParts(String client, String machineId, String path, EmsVehicleDto emsVehicleDto) throws URISyntaxException, IOException, InterruptedException {
        HttpRequest request = buildBaseRequest(client, machineId, path)
                .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(emsVehicleDto)))
                .build();
        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() != 200) {
            log.error("parts matching request failed with code {}, reason: {}", response.statusCode(), response.body());
        }
        return response.statusCode() == 200 ? mapper.readValue(response.body(), PartsMatchDto.class) : null;
    }

    public void sendDebug(String client, String machineId, DebugInfo info) throws Exception {
        if (info == null || info.getEvents() == null || info.getEvents().isEmpty()) {
            return;
        }
        info.setWsId(com.partskick.Global.workstationId);
        HttpRequest request = buildBaseRequest(client, machineId, "system-debug", "v3")
                .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(info)))
                .build();
        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() != 200) {
            log.error("failed to debug");
        }
    }

    public Boolean voidInvoice(String clientId, String machineId, InvoiceVoidRequest invoiceVoidRequest) throws URISyntaxException, IOException, InterruptedException {
        HttpRequest request = buildBaseRequest(clientId, machineId, "invoice-void", "v4")
                .POST(HttpRequest.BodyPublishers.ofString(mapper.writeValueAsString(invoiceVoidRequest)))
                .build();
        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());
        if (response.statusCode() != 200) {
            log.error("failed to void invoice {}", invoiceVoidRequest.getInvoiceNumber());
            throw new RuntimeException("failed to void invoice");
        }
        String message = response.body();
        return message.equalsIgnoreCase("success");
    }

    public Map<Integer, EmsPdfData> uploadEmsPdf(String client, String machineId, String workstation, java.io.File pdfFile) throws Exception {
        // Read the PDF file as bytes
        byte[] pdfBytes = java.nio.file.Files.readAllBytes(pdfFile.toPath());

        // Create a proper boundary for multipart - use a simpler format
        String boundary = "----WebKitFormBoundary" + System.currentTimeMillis();

        // Create the multipart body
        byte[] multipartBody = createProperMultipartBody(pdfBytes, pdfFile.getName(), boundary);

        log.debug("Uploading PDF file: {} ({} bytes) with boundary: {}", pdfFile.getName(), pdfBytes.length, boundary);

        // Log the request details for debugging
        log.debug("Content-Type: multipart/form-data; boundary={}", boundary);
        log.debug("Request body length: {} bytes", multipartBody.length);

        HttpRequest request = buildMultipartRequest(client, machineId, workstation, "upload-ems", "v5")
                .header("Content-Type", "multipart/form-data; boundary=" + boundary)
                .POST(HttpRequest.BodyPublishers.ofByteArray(multipartBody))
                .build();

        // Log all headers for debugging
        log.debug("Request headers:");
        request.headers().map().forEach((key, values) -> {
            log.debug("  {}: {}", key, values);
        });

        HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() != 200) {
            log.error("Failed to upload EMS PDF file: {} {}, status: {}, reason: {}",
                    pdfFile.getName(), pdfFile.getAbsolutePath(), response.statusCode(), response.body());
            throw new RuntimeException("Failed to upload EMS PDF file: " + response.body());
        }

        // Parse the response to Map<Integer, EmsPdfData>
        try {
            MapType mapType = mapper.getTypeFactory().constructMapType(
                    Map.class, Integer.class, EmsPdfData.class);
            return mapper.readValue(response.body(), mapType);
        } catch (Exception e) {
            log.error("Failed to parse EMS PDF upload response: {}", e.getMessage());
            throw new RuntimeException("Failed to parse EMS PDF upload response", e);
        }
    }

    private byte[] createProperMultipartBody(byte[] fileBytes, String fileName, String boundary) throws Exception {
        java.io.ByteArrayOutputStream outputStream = new java.io.ByteArrayOutputStream();

        String lineEnd = "\r\n";
        String twoHyphens = "--";

        // Write the opening boundary
        outputStream.write((twoHyphens + boundary + lineEnd).getBytes("UTF-8"));

        // Write the Content-Disposition header (server expects parameter name "file")
        String disposition = "Content-Disposition: form-data; name=\"file\"; filename=\"" + fileName + "\"" + lineEnd;
        outputStream.write(disposition.getBytes("UTF-8"));

        // Write the Content-Type header
        String contentType = "Content-Type: application/pdf" + lineEnd;
        outputStream.write(contentType.getBytes("UTF-8"));

        // Write empty line to separate headers from content
        outputStream.write(lineEnd.getBytes("UTF-8"));

        // Write the file content
        outputStream.write(fileBytes);

        // Write the closing boundary
        String closingBoundary = lineEnd + twoHyphens + boundary + twoHyphens + lineEnd;
        outputStream.write(closingBoundary.getBytes("UTF-8"));

        // Log the multipart body structure for debugging
        log.debug("Multipart body structure:");
        log.debug("  Opening boundary: {}", twoHyphens + boundary);
        log.debug("  Content-Disposition: {}", disposition.trim());
        log.debug("  Content-Type: {}", contentType.trim());
        log.debug("  File content length: {} bytes", fileBytes.length);
        log.debug("  Closing boundary: {}", closingBoundary.trim());

        return outputStream.toByteArray();
    }
}
