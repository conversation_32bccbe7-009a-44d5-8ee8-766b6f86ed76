package com.partskick.gui;

import com.partskick.model.Estimation;

import javax.swing.*;
import javax.swing.table.TableCellRenderer;
import java.awt.*;

public class EMSTable extends JTable {
    private EstimationTableModel estimationTableModel;

    public EMSTable(EstimationTableModel estimationTableModel) {
        super(estimationTableModel);
        this.estimationTableModel = estimationTableModel;
    }

    @Override
    public Component prepareRenderer(TableCellRenderer renderer, int row, int column) {
        Component c = super.prepareRenderer(renderer, row, column);
        Estimation db = estimationTableModel.getEMS(row);

        if (db.isAdjusted()) {
            c.setBackground(Color.lightGray);
        } else {
            c.setBackground(Color.WHITE);
        }
        return super.prepareRenderer(renderer, row, column);
    }
}
