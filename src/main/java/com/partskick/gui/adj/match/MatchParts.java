package com.partskick.gui.adj.match;

import com.partskick.parts.Invoice;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.text.NumberFormat;
import java.util.List;
import java.util.Locale;
import java.util.Set;

@Data
@AllArgsConstructor
@Builder
@Accessors(chain = true)

public class MatchParts {
    private static NumberFormat formatter = NumberFormat.getCurrencyInstance(Locale.US);

    private Long inventoryId;
    private String type;
    private Set<String> partsDescs;
    private Long pnId;
    private String partsNumber;
    private String cleanPn;
    private String clientId;
    private String clientName;
    private Integer vendorId;
    private String vendorName;
    private Integer qty;
    private Double listPrice;
    private Double cost;
    private String contactPerson;
    private String contactNumber;
    private String location;
    private List<Invoice> invoices;
    private boolean hasPicture;

    public String[] getPn() {
        return new String[]{"PN# " + partsNumber, String.join(" | ", partsDescs), inventoryId.toString()};
    }

    public String[] getPriceCostQty() {
        // format listPrice as currency
        return new String[]{"List Price: " + formatter.format(listPrice), "Cost: " + formatter.format(cost), "Qty: " + qty};
    }

    public String[] getClientContact() {
        return new String[]{vendorId != null ? vendorName : clientName, contactPerson + " @ " + contactNumber, location};
    }
}
