package com.partskick.gui.adj.match;

import com.partskick.parts.Invoice;

import javax.swing.*;
import javax.swing.table.TableCellRenderer;
import java.awt.*;

public class InvoiceHistoryTable extends JTable {
    private final InvoiceHistoryTableModel invoiceHistoryTableModel;

    public InvoiceHistoryTable(InvoiceHistoryTableModel invoiceHistoryTableModel) {
        super(invoiceHistoryTableModel);
        this.invoiceHistoryTableModel = invoiceHistoryTableModel;
    }

    @Override
    public Component prepareRenderer(TableCellRenderer renderer, int row, int column) {
        Component c = super.prepareRenderer(renderer, row, column);
        Invoice db = invoiceHistoryTableModel.getInvoice(row);

        if (db.isVoided()) {
            c.setBackground(Color.lightGray);
        } else {
            c.setBackground(Color.WHITE);
        }
        return super.prepareRenderer(renderer, row, column);
    }
}
