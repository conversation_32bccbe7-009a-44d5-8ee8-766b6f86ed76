package com.partskick.gui.adj.match;

import com.partskick.Global;
import com.partskick.Utils;
import com.partskick.client.ClientHelper;
import com.partskick.gui.adj.LineItemPanelListener;
import com.partskick.gui.common.*;
import com.partskick.gui.progress.ProgressIndeterminateDialog;
import com.partskick.model.Estimation;
import com.partskick.parts.*;
import lombok.extern.slf4j.Slf4j;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class MatchResultTable extends JDialog {
	private ProgressIndeterminateDialog progressDialog;
	private JTable table;
	private MatchTableModel tableModel;
	private Estimation estimation;

	public MatchResultTable(JFrame parent, List<MatchParts> matchPartsList, Estimation estimation, Integer lineNumber, String partsDesc, Integer lineItemQty, LineItemPanelListener lineItemPanelListener, InventorySource allowChangePrice) {
		super(parent, "", true);
		setIconImage(Utils.getCommonIcon(getClass()).getImage());
		this.progressDialog = DialogFactory.getProgressIndeterminateDialog();

		this.estimation = estimation;
		tableModel = new MatchTableModel();
		tableModel.setData(matchPartsList);
		table = new JTable(tableModel);
		table.setTableHeader(null);
		table.setRowHeight(table.getRowHeight() * 5);

		MultiLineTableCellWithTooltipRenderer leftRenderer = new MultiLineTableCellWithTooltipRenderer();
		leftRenderer.setAlignment(SwingConstants.LEFT);
		table.getColumnModel().getColumn(0).setCellRenderer(leftRenderer);
		MultiLineTableCellRenderer centerRenderer = new MultiLineTableCellRenderer();
		table.getColumnModel().getColumn(1).setCellRenderer(centerRenderer);
		MultiLineTableCellRenderer rightRenderer = new MultiLineTableCellRenderer();
		rightRenderer.setAlignment(SwingConstants.RIGHT);
		table.getColumnModel().getColumn(2).setCellRenderer(rightRenderer);
		table.setIntercellSpacing(new Dimension(5, 2));
		table.getColumnModel().getColumn(3).setCellRenderer(new ButtonRenderer());
		table.getColumnModel().getColumn(3).setCellEditor(new ButtonEditor(new PaCheckBox(), table));

		table.addMouseListener(new MouseAdapter() {
			@Override
			public void mouseClicked(MouseEvent evt) {
				if (evt.getClickCount() >= 2) {
					Point pnt = evt.getPoint();
					int row = table.rowAtPoint(pnt);
					MatchParts parts = tableModel.getParts(row);
					if (parts.getQty() <= 0) {
						JOptionPane.showMessageDialog(MatchResultTable.this, ResourceFactory.getBundle().getString("tt_no_inventory"));
						return;
					}
					InvoiceDialog dialog = new InvoiceDialog(parent, ResourceFactory.getBundle().getString("tt_invoice"), estimation, lineNumber, partsDesc, lineItemQty, lineItemPanelListener, allowChangePrice);

					SwingWorker<NewInvoiceResponse, Integer> historyWorker = new SwingWorker<>() {
						@Override
						protected void done() {
							progressDialog.setIndeterminate(false);
							progressDialog.setVisible(false);

							if (isCancelled()) return;
							try {
								NewInvoiceResponse response = get();
								if (response == null) {
									JOptionPane.showMessageDialog(MatchResultTable.this, ResourceFactory.getBundle().getString("ms_something_wrong"));
									return;
								}
								if (response.getNextInvoiceNumber() == null) {
									JOptionPane.showMessageDialog(MatchResultTable.this, ResourceFactory.getBundle().getString("msg_no_invoice_number"));
									return;
								}
								parts.setInvoices(response.getInvoices());
								Map<Long, Integer> count = new HashMap<>();
								for (Invoice invoice : parts.getInvoices()) {
									count.put(invoice.getPnId(), invoice.getQty());
								}
								String key = Utils.getEmsFileFullPath(estimation);
								Global.emsPartsMatchCount.put(key, count);
								Map<Long, Integer> partsCount = Global.emsFilePartsCount.get(key);
								Integer orderLimit = partsCount.getOrDefault(parts.getPnId(), parts.getQty());
								if (parts.getInvoices() != null) {
									List<Invoice> validInvoices = parts.getInvoices().stream().filter(i -> !i.isVoided()).toList();
									for (Invoice invoice : validInvoices) {
										orderLimit -= invoice.getQty();
									}
									if (orderLimit <= 0) {
										JOptionPane.showMessageDialog(MatchResultTable.this, ResourceFactory.getBundle().getString("msg_already_ordered"));
										return;
									}
								}
								orderLimit = Math.min(orderLimit, lineItemQty);
								if (orderLimit <= 0) {
									JOptionPane.showMessageDialog(MatchResultTable.this, ResourceFactory.getBundle().getString("tt_no_inventory"));
									return;
								}
								dialog.setParts(parts, orderLimit);
								dialog.setInvoiceNumber(response.getNextInvoiceNumber());
								dialog.setVisible(true);
							} catch (Exception e) {
								log.error(e.getMessage(), e);
								JOptionPane.showMessageDialog(MatchResultTable.this, e.getMessage());
							}
						}

						@Override
						protected NewInvoiceResponse doInBackground() throws URISyntaxException, IOException, InterruptedException {
							progressDialog.setVisible(true);
							progressDialog.setIndeterminate(true);
							return Global.httpService.getInvoiceHistory(Global.clientId, Global.machineId, new InvoiceHistoryRequest().setVin(estimation.getVin()).setEmsFileName(estimation.getFileName()).setPnId(parts.getPnId()).setWithNextId(true));
						}
					};
					historyWorker.execute();
				}
			}
		});

		setLayout(new BorderLayout());

		add(new JScrollPane(table), BorderLayout.CENTER);

		Dimension dimension = new Dimension(750, 400);
		table.getColumnModel().getColumn(0).setPreferredWidth(200);
		table.getColumnModel().getColumn(1).setPreferredWidth(200);
		table.getColumnModel().getColumn(2).setPreferredWidth(270);
		table.getColumnModel().getColumn(3).setPreferredWidth(80);
		setSize(dimension);
		setMinimumSize(dimension);
		setLocationRelativeTo(parent);

		if (Global.companyInvoicePreference == null || Global.taxes == null) {
			SwingWorker<Void, Void> worker = new SwingWorker<>() {
				@Override
				protected Void doInBackground() {
					try {
						if (Global.companyInvoicePreference == null) {
							Global.companyInvoicePreference = Global.httpService.getInvoicePreference(Global.clientId, Global.machineId);
						}
					} catch (Exception e) {
						log.error(e.getMessage(), e);
						Global.companyInvoicePreference = new InvoicePreferenceV2();
					}
					try {
						if (Global.taxes == null) {
							Global.taxes = Global.httpService.getTaxes(Global.clientId, Global.machineId);
							Global.clientProvinceTaxes = Global.taxes.stream().filter(t -> t.getProvinceState().equals(ClientHelper.getClient().getProvince())).toList();
						}
					} catch (Exception e) {
						log.error(e.getMessage(), e);
						Global.taxes = new ArrayList<>();
					}
					return null;
				}
			};
			worker.execute();
		}
	}

	public void updateData(List<MatchParts> matchPartsList) {
		tableModel.setData(matchPartsList);
		tableModel.fireTableDataChanged();
	}
}
