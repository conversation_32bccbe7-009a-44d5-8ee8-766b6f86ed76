package com.partskick.gui.adj.match;

import com.partskick.gui.common.ResourceFactory;
import com.partskick.parts.Invoice;

import javax.swing.table.DefaultTableModel;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;

public class InvoiceHistoryTableModel extends DefaultTableModel {

    private List<Invoice> db = new ArrayList<>();

    private String[] colNames = new String[11];

    public InvoiceHistoryTableModel() {
        super();
        ResourceBundle bundle = ResourceFactory.getBundle();
        colNames[0] = bundle.getString("lb_inv_number");
        colNames[1] = bundle.getString("lb_vin");
        colNames[2] = bundle.getString("lb_inv_date");
        colNames[3] = bundle.getString("lb_ems");
        colNames[4] = bundle.getString("lb_c_t_company");
        colNames[5] = bundle.getString("lb_inv_bill_to");
        colNames[6] = bundle.getString("lb_inv_ship_to");
        colNames[7] = bundle.getString("lb_inv_price");
        colNames[8] = bundle.getString("lb_inv_qty");
        colNames[9] = bundle.getString("lb_tax_rate");
        colNames[10] = bundle.getString("lb_inv_total");
    }

    @Override
    public boolean isCellEditable(int row, int column) {
        //all cells false
        return false;
    }

    @Override
    public String getColumnName(int column) {
        return colNames[column];
    }


    public void setData(List<Invoice> db) {
        this.db = db;
        fireTableDataChanged();
    }

    @Override
    public int getColumnCount() {
        return colNames.length;
    }

    @Override
    public int getRowCount() {
        return db == null ? 0 : db.size();
    }

    @Override
    public Object getValueAt(int row, int col) {
        Invoice invoice = db.get(row);
        switch (col) {
            case 0:
                return invoice.getInvoiceNumber();
            case 1:
                return invoice.getVin();
            case 2:
                return invoice.getInvoiceDate();
            case 3:
                return invoice.getEmsFileName();
            case 4:
                return invoice.getInvoicePreference().getCompanyName();
            case 5:
                return invoice.getInvoicePreference().getBillToName();
            case 6:
                return invoice.getInvoicePreference().getShipToName();
            case 7:
                return invoice.getPrice();
            case 8:
                return invoice.getQty();
            case 9:
                return invoice.getTaxRate();
            case 10:
                return invoice.getQty() * invoice.getPrice() * (1 + invoice.getTaxRate() / 100.0);
        }
        return null;
    }

    public Invoice getInvoice(int row) {
        return db.get(row);
    }
}
