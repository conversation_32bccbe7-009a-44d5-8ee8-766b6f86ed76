package com.partskick.gui;

import com.partskick.gui.common.ResourceFactory;
import com.partskick.gui.prefs.PrefsDialog;
import com.partskick.model.EmsOrder;
import com.partskick.ui.EmsSource;
import com.partskick.ui.EmsSourceHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.net.URL;
import java.util.ArrayList;
import java.util.ResourceBundle;
import java.util.prefs.Preferences;
import com.partskick.Utils;

@Slf4j
public class EmsToolbar extends JToolBar implements EmsToolBarListener {
	private JButton refreshButton;

	private JComboBox emsType;
	private JComboBox emsSource;
	private JComboBox orderBy;
	private JLabel numFiles;

	private ResourceBundle bundle;
	
	private EstimationPanelListener estimationPanelListener;
	private EmsOrder emsOrder;
	private int orderDirectIdx = 1;
	    private Preferences prefs = Utils.getPartsKickPrefs(Utils.getInstallationType());
	private JTextField searchText;
	private JButton searchButton;

	public EmsToolbar() {
		this.bundle = ResourceFactory.getBundle();

		emsOrder = new EmsOrder();
		// Get rid of the border if you want the toolbar draggable.
		setBorder(BorderFactory.createEtchedBorder());
		

		refreshButton = new JButton();
		refreshButton.setIcon(createIcon("/images/Refresh16.gif"));
		refreshButton.setToolTipText(bundle.getString("tp_refresh"));

		add(refreshButton);
		addSeparator();
		String[] types = new String[] {"All", bundle.getString("lb_new"), bundle.getString("lb_adjusted")};
		emsType = new JComboBox(types);
		emsType.setSelectedIndex(0);
		emsType.setMaximumSize(emsType.getPreferredSize());
		emsType.setToolTipText(bundle.getString("tp_type"));
/*
		add(emsType);
		addSeparator();
*/

		java.util.List<String> sl = new ArrayList<>();
		sl.add("All");
		sl.addAll(EmsSourceHelper.getAllSources().stream().filter(s -> StringUtils.isNotBlank(s.getExportPath())).map(EmsSource::getName).toList());

		String[] sources = sl.toArray(String[]::new);
		emsSource = new JComboBox(sources);
		emsSource.setSelectedIndex(0);
		emsSource.setToolTipText(bundle.getString("tp_source"));
		emsSource.setMaximumSize(emsSource.getPreferredSize());
		numFiles = new JLabel(bundle.getString("lb_files"));
/*
		addSeparator();
		add(emsSource);
*/
		addSeparator();
		add(numFiles);

		addSeparator();
		add(new JLabel(bundle.getString("lb_order_by")));

		String[] orderType = emsOrder.getEmsOrder().values().toArray(String[]::new);
		orderBy = new JComboBox(orderType);
		orderBy.setSelectedIndex(0);
		orderBy.setMaximumSize(orderBy.getPreferredSize());
		orderBy.setToolTipText(bundle.getString("tp_order"));
		add(orderBy);

		JButton orderButton = new JButton();
		Image image = createIcon("/images/up-down-icon-16.jpg").getImage();
		Image newImage = image.getScaledInstance(16, 16, java.awt.Image.SCALE_SMOOTH);
		orderButton.setIcon(new ImageIcon(newImage));
		orderButton.setToolTipText((orderDirectIdx == 0 ? bundle.getString("tp_order_change_desc") : bundle.getString("tp_order_change_asc")) + orderBy.getSelectedItem());

		add(orderButton);

		addSeparator();
		addSeparator();
		addSeparator();

		searchText = new JTextField();
		searchText.setSize(searchText.getPreferredSize());
		add(searchText);

		searchButton = new JButton();
		searchButton.setIcon(new ImageIcon(createIcon("/images/search.png").getImage().getScaledInstance(16, 16, java.awt.Image.SCALE_SMOOTH)));
		searchButton.setToolTipText(bundle.getString("tp_search"));

		add(searchButton);

		emsType.addActionListener(new ActionListener() {
			@Override
			public void actionPerformed(ActionEvent e) {
				estimationPanelListener.emsFiltered((String) emsType.getSelectedItem(), (String) emsSource.getSelectedItem());

			}
		});
		emsSource.addActionListener(new ActionListener() {

			@Override
			public void actionPerformed(ActionEvent event) {
				estimationPanelListener.emsFiltered((String) emsType.getSelectedItem(), (String) emsSource.getSelectedItem());
			}
		});

		ActionListener orderListener = new ActionListener() {

			@Override
			public void actionPerformed(ActionEvent event) {
				if (event.getSource() instanceof JButton && event.getSource() == orderButton) {
					if (orderDirectIdx == 0) {
						orderDirectIdx = 1;
					} else {
						orderDirectIdx = 0;
					}
				}
				orderButton.setToolTipText((orderDirectIdx == 0 ? bundle.getString("tp_order_change_desc") : bundle.getString("tp_order_change_asc")) + orderBy.getSelectedItem());
				estimationPanelListener.emsOrder(orderBy.getSelectedIndex(), orderDirectIdx);
			}
		};

		orderBy.addActionListener(orderListener);
		orderButton.addActionListener(orderListener);


		refreshButton.addActionListener(new ActionListener() {

			@Override
			public void actionPerformed(ActionEvent e) {
				estimationPanelListener.loadEstimations(prefs.getInt(PrefsDialog.AUTO_REMOVE_SOURCE, 0), prefs.getInt(PrefsDialog.AUTO_REMOVE_TARGET, 0));
			}
		});

		searchButton.addActionListener(new ActionListener() {
			@Override
			public void actionPerformed(ActionEvent e) {
				if (StringUtils.isNotBlank(searchText.getText())) {
					estimationPanelListener.search(searchText.getText().trim(), orderBy.getSelectedIndex());
				}
			}
		});
	}
	
	public ImageIcon createIcon(String path) {
		URL url = getClass().getResource(path);

		if(url == null) {
			log.error("Unable to load image: {}", path);
		}

		ImageIcon icon = new ImageIcon(url);

		return icon;
	}

	public void setEmsFilterListener(EstimationPanelListener estimationPanelListener) {
		this.estimationPanelListener = estimationPanelListener;
	}

	@Override
	public void fileNumChanged(int num) {
		numFiles.setText(num + " " + bundle.getString("lb_files"));
	}

	@Override
	public void setSource(String source) {
		emsSource.setSelectedItem(source);
	}

	@Override
	public String getSource() {
		return emsSource.getSelectedItem().toString();
	}

	@Override
	public void setOrder(int idx) {
		orderBy.setSelectedIndex(idx);
	}

	@Override
	public int getOrder() {
		return orderBy.getSelectedIndex();
	}

	@Override
	public void setDirection(int idx) {
		orderDirectIdx = idx;
	}

	@Override
	public int getDirection() {
		return orderDirectIdx;
	}

	@Override
	public String getType() {
		return emsType.getSelectedItem().toString();
	}

}
