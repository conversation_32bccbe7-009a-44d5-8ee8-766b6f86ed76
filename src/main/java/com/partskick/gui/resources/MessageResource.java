package com.partskick.gui.resources;

import java.util.ListResourceBundle;

public class MessageResource extends ListResourceBundle {

    @Override
    protected Object[][] getContents() {
        return new Object[][] {
                {"tt_title", "Parts Kick"},
                {"ms_not_supported", " is not supported."},
                {"ms_not_active", "Registered but has not been activated. Please contact customer service."},
                {"ms_something_wrong", "Something went wrong. Please contact customer service."},
                {"bt_save", "Save"},
                {"bt_cancel", "Cancel"},
                {"ms_exit_confirm", "Do you really want to exit the application?"},
                {"tt_exit_confirm", "Confirm Exit"},
                {"ms_load", "Loading estimates....."},
                {"ms_process", "Processing....."},
                {"ms_complete", "complete"},
                {"ms_network_error", "Connection to server failed, please check internet connection."},
                {"ms_ems_clean_fail", "remove old EMS file failed, please clean up manually in folder "},
                {"ms_export_clean_fail", "remove old processed file failed, please clean up manually in folder "},
                {"ms_not_found", "Not Found!"},
                {"ms_time_error", "Cannot get configuration from server, please check internet connection!"},
                {"ms_expired", "Free trial has expired"},
                {"ms_expiring_warn", "Free trial will be expired after Apr 30"},
                {"ms_check_network", "please make sure internet is available"},
                {"ms_force_upgrade", "We have a new version, the current version won't work anymore."},
                {"ms_force_upgrade_download", " to download the new version"},
                {"ms_force_upgrade_click", "Click Here "},
                {"ms_save_inv_prefer", "Overwrite the company wise preference?"},
                {"tt_confirm_save", "Confirm Save"},

                /* promo popup */
                {"bt_msg_close", "Close"},
                {"bt_msg_dont_show", "Don't Show this message again"},
                {"bt_msg_goto", "More Info"},

                /* menu -- begin */
                {"mu_prefs", "Preferences"},
                {"mu_file", "File"},
                {"mu_exit", "Exit"},
                {"mu_window", "Window"},
                {"mu_inventory", "Inventory"},
                {"mu_inventory_settings", "Settings"},
                {"mu_parts_list", "Parts List"},
                {"mu_invoice_list", "Invoice List"},
                {"tt_inventory_settings", "Settings"},
                /* menu -- end */

                /* toolbar -- begin */
                {"lb_files", "file(s)"},
                {"lb_order_by", "Order by: "},
                {"lb_order_filename", "File Name"},
                {"lb_order_filedate", "File Date"},
                {"lb_order_vin", "VIN"},
                {"lb_order_firstname", "First Name"},
                {"lb_order_lastname", "Last Name"},
                {"lb_order_make", "Make"},
                {"lb_order_model", "Model"},
                {"lb_order_roid", "RO Num"},
                {"lb_order_claim_num", "Claim Num"},
                {"lb_order_asc", "ASC"},
                {"lb_order_desc", "DESC"},
                {"tp_refresh", "Reload"},
                {"tp_type", "EMS"},
                {"tp_source", "EMS Source"},
                {"tp_order", "Order by"},
                {"tp_order_change_asc", "Change to ascending order on "},
                {"tp_order_change_desc", "Change to descending order on "},
                {"tp_search", "Search"},
                /* toolbar -- end */

                /* preference screen -- begin */

                {"tt_prefs", "Preferences"},
                {"lb_source_file_location", "Select Estimate Path"},
                {"lb_target_file_location", "Select Trader Path"},
                {"lb_est_adj_prefs", "PartsKick Preferences"},
                {"lb_est_path_prefs", "EMS file locations"},
                {"lb_est_type_prefs", "Keep Parts Type"},
                {"lb_est_file_loc_export", " Estimate Path"},
                {"lb_est_file_loc_trader", " Trader Path"},
                {"lb_disc_1", "Reduce "},
                {"lb_disc_2", " by %: "},
                {"lb_auto_push", "Enable Auto Commit"},
                {"lb_auto_delete", "# Remove old files # "},
                {"lb_auto_delete_ems", "Remove EMS files older than "},
                {"lb_auto_delete_ems_days", "days."},
                {"lb_auto_delete_adj", "Remove processed EMS files older than "},
                {"lb_auto_delete_adj_days", "days."},
                {"lb_manual_location", "Allow select Trader location manually"},
                {"ms_cannot_neg", " cannot less then 0"},
                {"ms_cannot_empty", " cannot be empty"},
                {"lb_auto_load_period", "EMS preload period: "},
                {"lb_auto_load_period1", "last 3 days"},
                {"lb_auto_load_period2", "last 10 days"},
                {"lb_auto_load_period3", "last 30 days"},
                {"lb_auto_load_period4", "last 90 days"},
                {"lb_auto_load_period5", "last 180 days"},
                {"lb_auto_load_period6", "last 1 year"},
                {"lb_auto_load_period7", "all the time"},
                {"lb_auto_load_number", "Max number of files: "},
                {"lb_auto_load_number1", "50"},
                {"lb_auto_load_number2", "100"},
                {"lb_auto_load_number3", "150"},
                {"lb_auto_load_number4", "200"},
                {"lb_auto_load_number5", "unlimited"},
                {"admin_privilege_required_title", "Administrator Privileges Required"},
                {"admin_privilege_required_message", "This application was installed for all users and requires administrator privileges to save system-wide preferences.\n\nWould you like to restart the application with administrator privileges?"},
                {"admin_privilege_restart_now", "Restart Now"},
                {"admin_privilege_restart_manual", "Restart Manually"},
                {"admin_privilege_cancel", "Cancel"},
                {"lb_dis_overwrite", "Shop discount: "},
                {"bt_sync", "Copy from Company Settings"},
                {"lb_inv_start", "Invoice Number Start From:"},
                {"ms_invalid_value", "Invalid Value"},
                {"ms_max_value", "Maximum value is"},
                {"ms_min_value", "Minimum value is"},

                /* preference screen -- end */

                /*estimation table -- begin*/
                {"lb_estimations", "Estimates"},
                {"lb_adjusted", "Processed"},
                {"lb_new", "New"},
                {"lb_year", "Year"},
                {"lb_make", "Make"},
                {"lb_model", "Model"},
                {"lb_vin", "VIN"},
                {"lb_plate", "Plate"},
                {"lb_color", "Color"},
                {"lb_vin_insurance", "VIN/Insurance"},
                {"lb_vehicle_info", "Vehicle Info"},
                {"lb_ems", "EMS"},

                /*estimation table -- end*/

                /*registration -- begin*/

                {"lb_registration", "Registration"},
                {"mu_registration", "Registration"},
                {"lb_reg_info", "Registration Information"},
                {"lb_client_name", "Client ID: "},
                {"lb_ws_name", "Work Station Name: "},
                {"lb_install_key", "Key: "},
                {"lb_ws_location", "Work Station Location: "},
                {"ms_reg_info_error", "Please fill all fields"},
                {"ms_reg_info_submitted", "Registered. Please wait for activation."},
                {"ms_reg_error", "Registration cannot be done this time, please try later."},
                {"lb_cn_def", "Your Client ID"},
                {"lb_sn_def", "Work Station Name"},
                {"bt_register", "Register"},
                {"ms_register_success", "Register Success, Please restart the application."},
                {"ms_ws_created", "Workstation just registered.\r\nThis program will exit, please run again."},

                /*registration -- end*/

                /*adjustment -- begin*/
                {"tt_adj", "PartsKick"},
                {"lb_replace", "Include"},
                {"lb_asParts", "as Parts A/M (& auto-reduce)"},
                {"tp_apply", "Apply AM Cost"},
                {"tp_cancel", "Cancel all changes"},
                {"tp_push", "Commit"},
                {"tp_reset", "Reset Processed EMS"},
                {"tp_pdf", "Download as PDF"},
                {"tp_search_parts", "Search Matching Parts Online"},
                {"lb_include", "Action"},
                {"lb_qty", "Qty"},
                {"lb_type", "Type"},
                {"lb_desc", "Description"},
                {"lb_part_num", "Part #"},
                {"lb_alt_part_num", "Alt Part #"},
                {"lb_glass", "Glass?"},
                {"lb_amt", "OEM"},
                {"lb_disc", "% off"},
                {"lb_amt_after_disc", "A/M"},
                {"ms_exit_confirm_without_push", "Reduce applied has not been committed, close the window will lose all the changes"},
                {"ms_no_empty", " cannot be empty"},
                {"ms_item", " for line item "},
                {"ms_no_type", "Did not select any parts type!"},
                {"tp_delete_processed", "Delete processed EMS"},
                {"ms_push_failed", "Failed to commit files"},
                {"lb_estimation", "Estimate"},
                {"ms_cannot_empty", "Estimate path is not defined"},
                {"ms_target_empty", "Trader path is not defined"},
                {"ms_ro_empty", "Please provide RO Number"},
                {"ms_cancel", "Cancel"},
                {"ms_continue", "Continue"},
                {"ms_0_warning", "Found parts with 0 price, do you want to continue?"},
                {"ms_0_stop", "Found parts with negative reduced price, cannot proceed"},
                {"ms_file_saved", "PDF file generated and saved as "},
                {"ms_open_file", "Open"},
                {"tt_warning", "***  WARNING ***"},
                {"tt_file_saved", "File Saved"},
                {"lb_adj_vin", "VIN: "},
                {"lb_adj_vehicle", "Vehicle: "},
                {"lb_adj_insu", "Insurance Company: "},
                {"lb_adj_owner", "Owner: "},
                {"lb_adj_claim", "Claim#: "},
                {"lb_adj_nc", "N/C"},
                {"lb_adj_no", "N/O"},
                {"ms_supplier_disc", " Shop discount will be applied ***   "},
                {"ms_open_folder", "Open Folder"},
                {"ms_open_error", "Desktop is not supported! Cannot open the file."},
                {"ms_open_folder_error", "Desktop is not supported! Cannot open the folder."},
                {"ms_final_word_apply", "Applying shop discount ..."},
                {"ms_search_apply", "Searching for alternative prices..."},
                {"ms_overwrite_apply", "applying overwrite"},
                {"ms_no_data", "There is no data"},
                {"tp_overwrite", "Shop discount"},
                {"tp_passthrough", "Commit without change"},
                {"tp_passthrough_succ", "EMS files committed"},
                {"tp_passthrough_fail", "Failed to commit EMS files"},
                {"ms_warning_pass", "Aftermarket price discount has not been applied! To sent the parts list as is click on Continue, To apply aftermarket discount click on Back"},
                {"ms_no_matching", "No matching parts found in inventory"},
                {"ms_no_parts", "No eligible parts to search"},
                {"lb_adj_extra_discount", "Shop Discount"},
                {"ms_invalid_disc", "Invalid discount"},
                {"tp_include_all", "Convert NC to INCL"},
                {"tp_locate_pdf", "Locate PN# From Estimate PDF"},
                {"tt_pdf_file_filter", "Estimate PDF Files"},
                {"ms_file_expired", "The session has expired, please close this windows and reload the estimate."},
                /*adjustment -- end*/

                /*match parts*/
                {"lb_yr", "Year"},
                {"lb_mk", "Make"},
                {"lb_md", "Model"},
                {"lb_pd", "Invoice"},
                {"lb_type", "Type"},
                {"lb_pn", "#"},
                {"lb_source", "Source"},
                {"lb_qty", "Qty"},
                {"lb_list_price", "List Price"},
                {"lb_cost", "Cost"},
                {"lb_cp", "Contact"},
                {"lb_cn", "Phone"},
                {"tt_search", "Searching..."},
                {"lb_location", "Location"},

                /*match parts -- end*/

                /* invoice */
                {"tt_invoice", "Invoice"},
                {"bt_issue_invoice", "Issue Invoice"},
                {"bt_download_invoice", "Download Invoice"},
                {"msg_select_qty", "Please select quantity"},
                {"lb_history", "Invoice History"},
                {"lb_inv_company_name", "Supplier Name*"},
                {"lb_inv_address1", "Address1*"},
                {"lb_inv_address2", "Address2"},
                {"lb_inv_phone", "Phone*"},
                {"lb_inv_email", "Email*"},
                {"lb_inv_bill_to", "Bill To"},
                {"lb_inv_city", "City*"},
                {"lb_inv_zip", "Post/Zip*"},
                {"lb_inv_province", "Province/State*"},
                {"lb_inv_tax_id", "Tax ID"},
                {"lb_inv_tax_name", "Tax Name"},
                {"lb_inv_tax_rate", "Tax Rate"},
                {"lb_inv_ship_to", "Ship To"},
                {"lb_inv_date", "Invoice Date"},
                {"lb_inv_number", "Invoice #"},
                {"lb_inv_sale_tax", "Sales Tax"},
                {"lb_inv_total_tax", "Total Tax"},
                {"lb_inv_total", "Total"},
                {"lb_inv_price", "Unit Price"},
                {"lb_inv_qty", "Qty"},
                {"lb_inv_pref_ow", "Save Preference"},
                {"lb_c_t_company", "Company"},
                {"lb_history_view", "View"},
                {"lb_inv_ship_to_same_as_bill_to", "Same as Bill To"},
                {"lb_bill_ship_to_name", "Name"},
                {"tt_no_inventory", "No enough inventory"},
                {"lb_inv_reduce_qty", "Remove this part from the inventory list after invoicing."},
                {"bt_preview_invoice", "Preview"},
                {"lb_company", "Company Default"},
                {"lb_local", "Local"},
                {"lb_inv_preference", "Invoice Preference"},
                {"msg_already_ordered", "Part already ordered"},
                {"lb_return_qty", "Return Qty back to inventory"},
                {"lb_inv_date_range", "Invoice Date"},
                {"bt_clear", "Clear"},
                {"lb_range_from", "From"},
                {"lb_range_to", "To"},
                {"btn_void_inv", "Void"},
                {"lb_tax_rate", "Tax Rate"},
                {"btn_print_inv", "Print"},
                {"msg_no_invoice_number", "Please setup invoice preference"},
                {"lb_supplier", "Supplier"},
                {"lb_default", "Default"},
                {"bt_delete", "Delete"},
                {"bt_save_supplier", "Save Supplier"},
                {"msg_confirm_delete_supplier", "Are you sure you want to delete this supplier?"},
                {"tt_confirm_delete", "Confirm Delete"},
                {"msg_cannot_delete_unsaved", "Cannot delete a supplier that hasn't been saved yet."},
                {"msg_supplier_saved", "Supplier saved successfully"},
                {"msg_supplier_save_failed", "Failed to save supplier"},
                {"msg_supplier_deleted", "Supplier deleted successfully"},
                {"msg_supplier_delete_failed", "Failed to delete supplier"},
                {"lb_new_supplier", "New Supplier..."},
                {"msg_invalid_email", "Invalid email format. Please enter a valid email address."},
                {"bt_custom", "Custom"},
                {"bt_update", "Update"},
                {"tt_invoice_preference", "Invoice Preference"}
                /* invoice -- end */

        };
    }
}

