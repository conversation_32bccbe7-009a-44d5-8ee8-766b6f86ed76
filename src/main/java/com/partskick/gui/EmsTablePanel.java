package com.partskick.gui;

import com.partskick.gui.adj.AdjustmentDialog;
import com.partskick.gui.common.MultiLineTableCellRenderer;
import com.partskick.gui.common.ResourceFactory;
import com.partskick.model.Estimation;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;

public class EmsTablePanel extends JPanel implements EmsTableListener {
	
	private JTable table;
	private EstimationTableModel tableModel;

	private AdjustmentDialog dialog;
	private List<Estimation> estimations;

	private EstimationPanelListener estimationPanelListener;
	public EmsTablePanel(JFrame parent) {
		tableModel = new EstimationTableModel();
		table = new EMSTable(tableModel);
		table.setTableHeader(null);
		table.setRowHeight(table.getRowHeight() * 5);

		MultiLineTableCellRenderer leftRenderer = new MultiLineTableCellRenderer();
		leftRenderer.setAlignment(SwingConstants.LEFT);
		table.getColumnModel().getColumn(0).setCellRenderer(leftRenderer);
		MultiLineTableCellRenderer centerRenderer = new MultiLineTableCellRenderer();
		table.getColumnModel().getColumn(1).setCellRenderer(centerRenderer);
		MultiLineTableCellRenderer rightRenderer = new MultiLineTableCellRenderer();
		rightRenderer.setAlignment(SwingConstants.RIGHT);
		table.getColumnModel().getColumn(2).setCellRenderer(rightRenderer);
		table.setIntercellSpacing(new Dimension(5, 2));
		dialog = new AdjustmentDialog(parent, this, ResourceFactory.getBundle().getString("tt_adj"));

		table.addMouseListener(new MouseAdapter() {
			public void mouseClicked(MouseEvent evt) {
				if (evt.getClickCount() >= 2) {
					Point pnt = evt.getPoint();
					int row = table.rowAtPoint(pnt);
					dialog.setEstimation(estimations.get(row));
//					Global.emsPartsMatchMap.clear();
					dialog.setVisible(true);

				}
			}
		});

		setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
		setLayout(new BorderLayout());
		
		add(new JScrollPane(table), BorderLayout.CENTER);
	}
	
	@Override
	public void refresh() {
		tableModel.fireTableDataChanged();
	}

	@Override
	public void setEstimation(Estimation estimation) {
		dialog.setEstimation(estimation);
		dialog.setVisible(true);
	}

	public void setData(List<Estimation> model) {
		tableModel.setData(model);
		this.estimations = model;
	}

	public void setEstimationPanelListener(EstimationPanelListener estimationPanelListener) {
		this.estimationPanelListener = estimationPanelListener;
		this.estimationPanelListener.setAdjDialog(dialog);
	}
}
