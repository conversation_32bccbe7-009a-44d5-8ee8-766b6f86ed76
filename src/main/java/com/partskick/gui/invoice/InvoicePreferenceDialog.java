package com.partskick.gui.invoice;

import com.partskick.Global;
import com.partskick.Utils;
import com.partskick.client.Tax;
import com.partskick.gui.common.PaCheckBox;
import com.partskick.gui.common.PaPanel;
import com.partskick.parts.InvoicePreferenceClient;
import com.partskick.parts.InvoicePreferenceSupplier;
import com.partskick.parts.InvoicePreferenceV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

import javax.swing.*;
import javax.swing.border.Border;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.ItemEvent;
import java.util.List;
import java.util.ResourceBundle;

@Slf4j
public class InvoicePreferenceDialog extends JDialog {

    private PaPanel ePanel;
    private PaPanel buttonPanel;
    private JButton saveButton;
    private JTextField companyNameField;
    private JTextField companyAddress1Field;
    private JTextField companyAddress2Field;
    private JTextField companyCityField;
    private JComboBox companyStateCombo;
    private JTextField companyZipField;
    private JTextField companyPhone;
    private JTextField companyEmail;
    private JTextField companyTaxId;
    private JTextField companyTaxRate;
    private JTextField companyTaxName;

    private JTextField billToNameField;
    private JTextField billToAddress1Field;
    private JTextField billToAddress2Field;
    private JTextField billToCityField;
    private JComboBox billToStateCombo;
    private JTextField billToZipField;
    private JTextField shipToNameField;
    private JTextField shipToAddress1Field;
    private JTextField shipToAddress2Field;
    private JTextField shipToCityField;
    private JComboBox shipToStateCombo;
    private JTextField shipToZipField;

    private JCheckBox shipToSameAsBillTo;

    private InvoicePreferenceV2 invoicePreference = Global.companyInvoicePreference;
    private List<InvoicePreferenceSupplier> suppliers = Global.companyInvoicePreference.getSuppliers();
    private InvoicePreferenceSupplier invoicePreferenceSupplier;
    private InvoicePreferenceClient invoicePreferenceClient;

    private JComboBox<String> supplierComboBox;
    private JCheckBox defaultSupplierCheckbox;

    private PaPanel ePanelLocal;
    private PaPanel buttonPanelLocal;
    private JButton saveButtonLocal;
    private JTextField companyNameFieldLocal;
    private JTextField companyAddress1FieldLocal;
    private JTextField companyAddress2FieldLocal;
    private JTextField companyCityFieldLocal;
    private JComboBox companyStateComboLocal;
    private JTextField companyZipFieldLocal;
    private JTextField companyPhoneLocal;
    private JTextField companyEmailLocal;
    private JTextField companyTaxIdLocal;
    private JTextField companyTaxRateLocal;
    private JTextField companyTaxNameLocal;

    private JTextField billToNameFieldLocal;
    private JTextField billToAddress1FieldLocal;
    private JTextField billToAddress2FieldLocal;
    private JTextField billToCityFieldLocal;
    private JComboBox billToStateComboLocal;
    private JTextField billToZipFieldLocal;
    private JTextField shipToNameFieldLocal;
    private JTextField shipToAddress1FieldLocal;
    private JTextField shipToAddress2FieldLocal;
    private JTextField shipToCityFieldLocal;
    private JComboBox shipToStateComboLocal;
    private JTextField shipToZipFieldLocal;

    private JCheckBox shipToSameAsBillToLocal;

    private InvoicePreferenceClient invoicePreferenceLocal = Global.localInvoicePreference.getClient();
    private InvoicePreferenceSupplier invoicePreferenceSupplierLocal = Global.localInvoicePreference.getSuppliers().get(0);

    private ResourceBundle bundle;
    private IInvoicePreferenceListener invoicePreferenceListener;


    public InvoicePreferenceDialog(JFrame parent, String title, ResourceBundle bundle, IInvoicePreferenceListener invoicePreferenceListener) {
        super(parent, title, true);
        this.invoicePreferenceListener = invoicePreferenceListener;
        // Initialize supplier - use the first one from the list if available
        if (!suppliers.isEmpty()) {
            invoicePreferenceSupplier = suppliers.get(0);
        } else {
            // We'll create a new supplier when the "New Supplier..." option is selected
            invoicePreferenceSupplier = null;
        }

        invoicePreferenceClient = invoicePreferenceListener.getInvoicePreferenceClient();
        invoicePreferenceSupplier = invoicePreferenceListener.getInvoicePreferenceSupplier();

        this.bundle = bundle;
        setIconImage(Utils.getCommonIcon(getClass()).getImage());
        setLayout(new BorderLayout());
        JTabbedPane tabbedPane = new JTabbedPane();
        add(tabbedPane, BorderLayout.CENTER);
        ePanel = new PaPanel();
        buttonPanel = new PaPanel();

        // Create a JPanel to hold all the content
        JPanel contentPanel = new JPanel(new BorderLayout());
        contentPanel.add(ePanel, BorderLayout.CENTER);

        // Create a JScrollPane and add the content panel to it
        JScrollPane scrollPane = new JScrollPane(contentPanel);
        tabbedPane.add("Company Preference", scrollPane);

        int space = 15;
        Border spaceBorder = BorderFactory.createEmptyBorder(space, space, space, space);
        TitledBorder titleBorder = BorderFactory.createTitledBorder(bundle.getString("lb_pd"));
        ePanel.setBorder(BorderFactory.createCompoundBorder(spaceBorder, titleBorder));
        ePanel.setLayout(new GridBagLayout());

        GridBagConstraints gc = new GridBagConstraints();

        Insets rightPadding = new Insets(5, 0, 5, 15);
        Insets leftPadding = new Insets(5, 15, 5, 0);
        Insets centerPadding = new Insets(5, 15, 5, 15);

        gc.fill = GridBagConstraints.NONE;
        gc.anchor = GridBagConstraints.WEST;

        gc.gridx = 0;
        gc.gridy = 0;

        gc.insets = leftPadding;

        // Add supplier dropdown
        PaPanel supplierPanel = new PaPanel();
        supplierPanel.setLayout(new FlowLayout());
        supplierPanel.add(new JLabel(bundle.getString("lb_supplier") + ":"));
        supplierComboBox = new JComboBox<>();

        // Populate the dropdown with existing supplier names
        for (int i = 0; i < suppliers.size(); i++) {
            InvoicePreferenceSupplier s = suppliers.get(i);
            String displayName = s.getCompanyName();
            if (displayName == null || displayName.isEmpty()) {
                displayName = "Supplier " + (i + 1);
            }
            supplierComboBox.addItem(displayName);
        }

        // Always add "New Supplier..." option at the end
        supplierComboBox.addItem(bundle.getString("lb_new_supplier"));

        // Add listener to update fields when a different supplier is selected
        supplierComboBox.addActionListener(e -> {
            int selectedIndex = supplierComboBox.getSelectedIndex();

            // Check if "New Supplier" option is selected (last item in the dropdown)
            if (selectedIndex == supplierComboBox.getItemCount() - 1) {
                // Create a new supplier
                invoicePreferenceSupplier = new InvoicePreferenceSupplier();

                // We don't add the supplier to the list until it's saved
                // Just create a new supplier object and update the UI

                // Clear all fields for the new supplier
                clearSupplierFields();

                // Set default to false for new supplier
                defaultSupplierCheckbox.setSelected(false);

                // Initialize tax fields based on the selected province
                setTaxFields();
            } else if (selectedIndex >= 0 && selectedIndex < suppliers.size()) {
                invoicePreferenceSupplier = suppliers.get(selectedIndex);
                updateSupplierFields();

                // Update the default checkbox state
                defaultSupplierCheckbox.setSelected(invoicePreferenceSupplier.getIsDefault());
            } else {
                // This should not happen, but just in case
                log.warn("Invalid supplier index selected: " + selectedIndex);
                clearSupplierFields();
                defaultSupplierCheckbox.setSelected(false);
            }
        });

        supplierPanel.add(supplierComboBox);

        // Add Default checkbox
        defaultSupplierCheckbox = new JCheckBox(bundle.getString("lb_default"));
        defaultSupplierCheckbox.setBackground(Utils.DEFAULT_BKC);
        defaultSupplierCheckbox.setEnabled(false);
        supplierPanel.add(defaultSupplierCheckbox);

        ePanel.add(supplierPanel, gc);

        gc.gridy++;
        gc.gridx = 0;

        PaPanel companyPanel = new PaPanel();
        companyPanel.setLayout(new FlowLayout(FlowLayout.LEFT));
        companyPanel.add(createAlignedLabel(bundle.getString("lb_inv_company_name") + ":"));
        companyNameField = new JTextField("");
        companyNameField.setColumns(20);
        companyPanel.add(companyNameField);
        ePanel.add(companyPanel, gc);

        gc.gridy++;
        gc.gridx = 0;

        gc.insets = leftPadding;

        PaPanel companyAdd1Panel = new PaPanel();
        companyAdd1Panel.setLayout(new FlowLayout(FlowLayout.LEFT));
        companyAdd1Panel.add(createAlignedLabel(bundle.getString("lb_inv_address1") + ":"));
        companyAddress1Field = new JTextField("");
        companyAddress1Field.setColumns(20);
        companyAdd1Panel.add(companyAddress1Field);
        ePanel.add(companyAdd1Panel, gc);

        gc.gridx++;

        gc.insets = centerPadding;

        PaPanel companyAdd2Panel = new PaPanel();
        companyAdd2Panel.setLayout(new FlowLayout(FlowLayout.LEFT));
        companyAdd2Panel.add(createAlignedLabel(bundle.getString("lb_inv_address2") + ":"));
        companyAddress2Field = new JTextField("");
        companyAddress2Field.setColumns(20);
        companyAdd2Panel.add(companyAddress2Field);
        ePanel.add(companyAdd2Panel, gc);

        gc.gridy++;
        gc.gridx = 0;

        gc.insets = leftPadding;

        PaPanel companyCityPanel = new PaPanel();
        companyCityPanel.setLayout(new FlowLayout(FlowLayout.LEFT));
        companyCityPanel.add(createAlignedLabel(bundle.getString("lb_inv_city") + ":"));
        companyCityField = new JTextField("");
        companyCityField.setColumns(10);
        companyCityPanel.add(companyCityField);
        ePanel.add(companyCityPanel, gc);


        gc.gridx++;

        gc.insets = centerPadding;

        PaPanel companyZipPanel = new PaPanel();
        companyZipPanel.setLayout(new FlowLayout(FlowLayout.LEFT));
        companyZipPanel.add(createAlignedLabel(bundle.getString("lb_inv_zip") + ":"));
        companyZipField = new JTextField("");
        companyZipField.setColumns(5);
        companyZipPanel.add(companyZipField);
        ePanel.add(companyZipPanel, gc);

        gc.gridx++;

        gc.insets = rightPadding;

        PaPanel companyProvincePanel = new PaPanel();
        companyProvincePanel.setLayout(new FlowLayout(FlowLayout.LEFT));
        companyProvincePanel.add(createAlignedLabel(bundle.getString("lb_inv_province") + ":"));
        companyStateCombo = new JComboBox<String>();
        Global.provinces.forEach(companyStateCombo::addItem);
        companyStateCombo.addItemListener(e -> {
            setTaxFields();
        });
        companyProvincePanel.add(companyStateCombo);
        ePanel.add(companyProvincePanel, gc);

        gc.gridy++;
        gc.gridx = 0;

        gc.insets = leftPadding;

        PaPanel companyPhonePanel = new PaPanel();
        companyPhonePanel.setLayout(new FlowLayout(FlowLayout.LEFT));
        companyPhonePanel.add(createAlignedLabel(bundle.getString("lb_inv_phone") + ":"));
        companyPhone = new JTextField("");
        companyPhone.setColumns(10);
        companyPhonePanel.add(companyPhone);
        ePanel.add(companyPhonePanel, gc);


        gc.gridx++;

        gc.insets = centerPadding;

        PaPanel companyEmailPanel = new PaPanel();
        companyEmailPanel.setLayout(new FlowLayout(FlowLayout.LEFT));
        companyEmailPanel.add(createAlignedLabel(bundle.getString("lb_inv_email") + ":"));
        companyEmail = new JTextField("");
        companyEmail.setColumns(25);
        companyEmailPanel.add(companyEmail);
        ePanel.add(companyEmailPanel, gc);

        gc.gridy++;
        gc.gridx = 0;

        gc.insets = leftPadding;

        PaPanel supplierTaxPanel = new PaPanel();
        supplierTaxPanel.setLayout(new FlowLayout(FlowLayout.LEFT));
        supplierTaxPanel.add(createAlignedLabel(bundle.getString("lb_inv_tax_id") + ":"));
        companyTaxId = new JTextField("");
        companyTaxId.setColumns(20);
        supplierTaxPanel.add(companyTaxId);
        ePanel.add(supplierTaxPanel, gc);


        gc.gridx++;

        gc.insets = centerPadding;

        PaPanel supplierTaxNamePanel = new PaPanel();
        supplierTaxNamePanel.setLayout(new FlowLayout(FlowLayout.LEFT));
        supplierTaxNamePanel.add(createAlignedLabel(bundle.getString("lb_inv_tax_name") + ":"));
        companyTaxName = new JTextField("");
        companyTaxName.setColumns(20);
        if (Global.country.equals("CAN")) {
            companyTaxName.setEditable(false);
        }
        supplierTaxNamePanel.add(companyTaxName);
        ePanel.add(supplierTaxNamePanel, gc);

        gc.gridx++;

        gc.insets = rightPadding;

        PaPanel supplierTaxRatePanel = new PaPanel();
        supplierTaxRatePanel.setLayout(new FlowLayout(FlowLayout.LEFT));
        supplierTaxRatePanel.add(createAlignedLabel(bundle.getString("lb_inv_tax_rate") + ":"));
        if (Global.country.equals("CAN")) {
            companyTaxRate = new JTextField(String.valueOf(Global.clientProvinceTaxes.stream().mapToDouble(Tax::getRate).sum()));
        } else {
            companyTaxRate = new JTextField("0.0");
        }
        companyTaxRate.setColumns(5);
        if (Global.country.equals("CAN")) {
            companyTaxRate.setEditable(false);
        }
        supplierTaxRatePanel.add(companyTaxRate);
        supplierTaxRatePanel.add(new JLabel("%"));
        ePanel.add(supplierTaxRatePanel, gc);


        // add a horizontal line
        gc.gridy++;
        gc.gridx = 0;
        gc.gridwidth = 4;
        gc.fill = GridBagConstraints.HORIZONTAL;
        gc.insets = new Insets(5, 0, 5, 0);
        ePanel.add(new JSeparator(SwingConstants.HORIZONTAL), gc);
        gc.fill = GridBagConstraints.NONE;
        gc.gridwidth = 1;

        // Bill To

        gc.gridx = 0;
        gc.gridy++;

        gc.insets = leftPadding;

        PaPanel billToSectionPanel = new PaPanel();
        billToSectionPanel.setLayout(new FlowLayout());
        billToSectionPanel.add(new JLabel(" ** " + bundle.getString("lb_inv_bill_to").toUpperCase() + " ** "));
        ePanel.add(billToSectionPanel, gc);

        gc.gridx = 0;
        gc.gridy++;

        gc.insets = leftPadding;

        PaPanel billToPanel = new PaPanel();
        billToPanel.setLayout(new FlowLayout(FlowLayout.LEFT));
        billToPanel.add(createAlignedLabel(bundle.getString("lb_bill_ship_to_name") + ":"));
        billToNameField = new JTextField(invoicePreferenceClient.getBillToName());
        billToNameField.setEditable(false);
        billToNameField.setColumns(20);
        billToPanel.add(billToNameField);
        ePanel.add(billToPanel, gc);

        gc.gridy++;
        gc.gridx = 0;

        gc.insets = leftPadding;

        PaPanel billToAdd1 = new PaPanel();
        billToAdd1.setLayout(new FlowLayout(FlowLayout.LEFT));
        billToAdd1.add(createAlignedLabel(bundle.getString("lb_inv_address1") + ":"));
        billToAddress1Field = new JTextField(invoicePreferenceClient.getBillToAddress1());
        billToAddress1Field.setColumns(20);
        billToAddress1Field.setEditable(false);
        billToAdd1.add(billToAddress1Field);
        ePanel.add(billToAdd1, gc);

        gc.gridx++;

        gc.insets = centerPadding;

        PaPanel billToAdd2 = new PaPanel();
        billToAdd2.setLayout(new FlowLayout(FlowLayout.LEFT));
        billToAdd2.add(createAlignedLabel(bundle.getString("lb_inv_address2") + ":"));
        billToAddress2Field = new JTextField(invoicePreferenceClient.getBillToAddress2());
        billToAddress2Field.setColumns(20);
        billToAddress2Field.setEditable(false);
        billToAdd2.add(billToAddress2Field);
        ePanel.add(billToAdd2, gc);

        gc.gridy++;
        gc.gridx = 0;

        gc.insets = leftPadding;

        PaPanel billToCity = new PaPanel();
        billToCity.setLayout(new FlowLayout(FlowLayout.LEFT));
        billToCity.add(createAlignedLabel(bundle.getString("lb_inv_city") + ":"));
        billToCityField = new JTextField(invoicePreferenceClient.getBillToCity());
        billToCityField.setColumns(10);
        billToCityField.setEditable(false);
        billToCity.add(billToCityField);
        ePanel.add(billToCity, gc);


        gc.gridx++;

        gc.insets = centerPadding;

        PaPanel billToZip = new PaPanel();
        billToZip.setLayout(new FlowLayout(FlowLayout.LEFT));
        billToZip.add(createAlignedLabel(bundle.getString("lb_inv_zip") + ":"));
        billToZipField = new JTextField(invoicePreferenceClient.getBillToZip());
        billToZipField.setColumns(5);
        billToZipField.setEditable(false);
        billToZip.add(billToZipField);
        ePanel.add(billToZip, gc);

        gc.gridx++;

        gc.insets = rightPadding;

        PaPanel billToProvince = new PaPanel();
        billToProvince.setLayout(new FlowLayout(FlowLayout.LEFT));
        billToProvince.add(createAlignedLabel(bundle.getString("lb_inv_province") + ":"));
        billToStateCombo = new JComboBox<String>(new String[]{invoicePreferenceClient.getBillToProvince()});
        billToStateCombo.setEditable(false);
        billToProvince.add(billToStateCombo);
        ePanel.add(billToProvince, gc);

        // Ship To
        gc.gridx = 0;
        gc.gridy++;

        gc.insets = leftPadding;

        PaPanel shipToSectionPanel = new PaPanel();
        shipToSectionPanel.setLayout(new FlowLayout());
        shipToSectionPanel.add(new JLabel(" ** " + bundle.getString("lb_inv_ship_to").toUpperCase() + " ** "));
        shipToSameAsBillTo = new PaCheckBox();
        shipToSameAsBillTo.setText(bundle.getString("lb_inv_ship_to_same_as_bill_to"));
        shipToSectionPanel.add(shipToSameAsBillTo);
        ePanel.add(shipToSectionPanel, gc);
        // add event listener
        shipToSameAsBillTo.addItemListener(e -> {
            if (e.getStateChange() == ItemEvent.SELECTED) {
                shipToNameField.setText(billToNameField.getText());
                shipToAddress1Field.setText(billToAddress1Field.getText());
                shipToAddress2Field.setText(billToAddress2Field.getText());
                shipToCityField.setText(billToCityField.getText());
                shipToZipField.setText(billToZipField.getText());
                shipToStateCombo.setSelectedItem(billToStateCombo.getSelectedItem());

                // Disable all ship to fields
                disableShipToFields(true);

                // Force UI update
                SwingUtilities.invokeLater(() -> {
                    shipToNameField.repaint();
                    shipToAddress1Field.repaint();
                    shipToAddress2Field.repaint();
                    shipToCityField.repaint();
                    shipToZipField.repaint();
                    shipToStateCombo.repaint();
                });
            } else {
                // Enable all ship to fields
                disableShipToFields(false);

                // Force UI update
                SwingUtilities.invokeLater(() -> {
                    shipToNameField.repaint();
                    shipToAddress1Field.repaint();
                    shipToAddress2Field.repaint();
                    shipToCityField.repaint();
                    shipToZipField.repaint();
                    shipToStateCombo.repaint();
                });
            }
        });


        gc.gridx = 0;
        gc.gridy++;

        gc.insets = leftPadding;

        PaPanel shipToPanel = new PaPanel();
        shipToPanel.setLayout(new FlowLayout(FlowLayout.LEFT));
        shipToPanel.add(createAlignedLabel(bundle.getString("lb_bill_ship_to_name") + ":"));
        shipToNameField = new JTextField(invoicePreferenceClient.getShipToName());
        shipToNameField.setColumns(20);
        shipToPanel.add(shipToNameField);
        ePanel.add(shipToPanel, gc);

        gc.gridy++;
        gc.gridx = 0;

        gc.insets = leftPadding;

        PaPanel shipToAdd1 = new PaPanel();
        shipToAdd1.setLayout(new FlowLayout(FlowLayout.LEFT));
        shipToAdd1.add(createAlignedLabel(bundle.getString("lb_inv_address1") + ":"));
        shipToAddress1Field = new JTextField(invoicePreferenceClient.getShipToAddress1());
        shipToAddress1Field.setColumns(20);
        shipToAdd1.add(shipToAddress1Field);
        ePanel.add(shipToAdd1, gc);

        gc.gridx++;

        gc.insets = centerPadding;

        PaPanel shipToAdd2 = new PaPanel();
        shipToAdd2.setLayout(new FlowLayout(FlowLayout.LEFT));
        shipToAdd2.add(createAlignedLabel(bundle.getString("lb_inv_address2") + ":"));
        shipToAddress2Field = new JTextField(invoicePreferenceClient.getShipToAddress2());
        shipToAddress2Field.setColumns(20);
        shipToAdd2.add(shipToAddress2Field);
        ePanel.add(shipToAdd2, gc);

        gc.gridy++;
        gc.gridx = 0;

        gc.insets = leftPadding;

        PaPanel shipToCity = new PaPanel();
        shipToCity.setLayout(new FlowLayout(FlowLayout.LEFT));
        shipToCity.add(createAlignedLabel(bundle.getString("lb_inv_city") + ":"));
        shipToCityField = new JTextField(invoicePreferenceClient.getShipToCity());
        shipToCityField.setColumns(10);
        shipToCity.add(shipToCityField);
        ePanel.add(shipToCity, gc);

        gc.gridx++;

        gc.insets = centerPadding;

        PaPanel shipToZip = new PaPanel();
        shipToZip.setLayout(new FlowLayout(FlowLayout.LEFT));
        shipToZip.add(createAlignedLabel(bundle.getString("lb_inv_zip") + ":"));
        shipToZipField = new JTextField(invoicePreferenceClient.getShipToZip());
        shipToZipField.setColumns(5);
        shipToZip.add(shipToZipField);
        ePanel.add(shipToZip, gc);

        gc.gridx++;

        gc.insets = rightPadding;

        PaPanel shipToProvince = new PaPanel();
        shipToProvince.setLayout(new FlowLayout(FlowLayout.LEFT));
        shipToProvince.add(createAlignedLabel(bundle.getString("lb_inv_province") + ":"));
        shipToStateCombo = new JComboBox<String>();
        Global.provinces.forEach(shipToStateCombo::addItem);
        shipToProvince.add(shipToStateCombo);
        ePanel.add(shipToProvince, gc);

        // buttons

        gc.gridx = 0;
        gc.gridy++;

        gc.insets = leftPadding;
        buttonPanel.setLayout(new FlowLayout());
        saveButton = new JButton(bundle.getString("bt_update"));
        buttonPanel.add(saveButton, BorderLayout.WEST);

        saveButton.addActionListener(e -> {
            // Validate both supplier and bill/ship data
            String error = validateData();
            if (error != null) {
                JOptionPane.showMessageDialog(this, error);
                return;
            }
            updatePreference();
        });

        ePanel.add(buttonPanel, gc);

        // local preference
        ePanelLocal = new PaPanel();
        buttonPanelLocal = new PaPanel();

        // Create a JPanel to hold all the content
        JPanel contentPanelLocal = new JPanel(new BorderLayout());
        contentPanelLocal.add(ePanelLocal, BorderLayout.NORTH);

        // Create a JScrollPane and add the content panel to it
        JScrollPane scrollPaneLocal = new JScrollPane(contentPanelLocal);
        tabbedPane.add("Local Preference", scrollPaneLocal);

        Border spaceBorderLocal = BorderFactory.createEmptyBorder(space, space, space, space);
        TitledBorder titleBorderLocal = BorderFactory.createTitledBorder(bundle.getString("lb_pd"));
        ePanelLocal.setBorder(BorderFactory.createCompoundBorder(spaceBorderLocal, titleBorderLocal));
        ePanelLocal.setLayout(new GridBagLayout());

        gc = new GridBagConstraints();
        Insets rightPaddingLocal = new Insets(5, 0, 5, 15);
        Insets leftPaddingLocal = new Insets(5, 15, 5, 0);
        Insets centerPaddingLocal = new Insets(5, 15, 5, 15);

        gc.fill = GridBagConstraints.NONE;
        gc.anchor = GridBagConstraints.WEST;

        gc.gridx = 0;
        gc.gridy = 0;

        gc.insets = leftPaddingLocal;

        PaPanel companyPanelLocal = new PaPanel();
        companyPanelLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        companyPanelLocal.add(createAlignedLabel(bundle.getString("lb_inv_company_name") + ":"));
        companyNameFieldLocal = new JTextField(invoicePreferenceSupplierLocal.getCompanyName());
        companyNameFieldLocal.setColumns(20);
        companyPanelLocal.add(companyNameFieldLocal);
        ePanelLocal.add(companyPanelLocal, gc);

        gc.gridy++;
        gc.gridx = 0;

        gc.insets = leftPaddingLocal;

        PaPanel companyAdd1PanelLocal = new PaPanel();
        companyAdd1PanelLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        companyAdd1PanelLocal.add(createAlignedLabel(bundle.getString("lb_inv_address1") + ":"));
        companyAddress1FieldLocal = new JTextField(invoicePreferenceSupplierLocal.getCompanyAddress1());
        companyAddress1FieldLocal.setColumns(20);
        companyAdd1PanelLocal.add(companyAddress1FieldLocal);
        ePanelLocal.add(companyAdd1PanelLocal, gc);

        gc.gridx++;

        gc.insets = centerPaddingLocal;

        PaPanel companyAdd2PanelLocal = new PaPanel();
        companyAdd2PanelLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        companyAdd2PanelLocal.add(createAlignedLabel(bundle.getString("lb_inv_address2") + ":"));
        companyAddress2FieldLocal = new JTextField(invoicePreferenceSupplierLocal.getCompanyAddress2());
        companyAddress2FieldLocal.setColumns(20);
        companyAdd2PanelLocal.add(companyAddress2FieldLocal);
        ePanelLocal.add(companyAdd2PanelLocal, gc);

        gc.gridy++;
        gc.gridx = 0;

        gc.insets = leftPaddingLocal;

        PaPanel companyCityPanelLocal = new PaPanel();
        companyCityPanelLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        companyCityPanelLocal.add(createAlignedLabel(bundle.getString("lb_inv_city") + ":"));
        companyCityFieldLocal = new JTextField(invoicePreferenceSupplierLocal.getCompanyCity());
        companyCityFieldLocal.setColumns(10);
        companyCityPanelLocal.add(companyCityFieldLocal);
        ePanelLocal.add(companyCityPanelLocal, gc);


        gc.gridx++;

        gc.insets = centerPaddingLocal;

        PaPanel companyZipPanelLocal = new PaPanel();
        companyZipPanelLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        companyZipPanelLocal.add(createAlignedLabel(bundle.getString("lb_inv_zip") + ":"));
        companyZipFieldLocal = new JTextField(invoicePreferenceSupplierLocal.getCompanyZip());
        companyZipFieldLocal.setColumns(5);
        companyZipPanelLocal.add(companyZipFieldLocal);
        ePanelLocal.add(companyZipPanelLocal, gc);

        gc.gridx++;

        gc.insets = rightPaddingLocal;

        PaPanel companyProvincePanelLocal = new PaPanel();
        companyProvincePanelLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        companyProvincePanelLocal.add(createAlignedLabel(bundle.getString("lb_inv_province") + ":"));
        companyStateComboLocal = new JComboBox<String>();
        Global.provinces.forEach(companyStateComboLocal::addItem);
        companyStateComboLocal.addActionListener(e -> {
            setTaxFieldsLocal();
        });
        companyProvincePanelLocal.add(companyStateComboLocal);
        ePanelLocal.add(companyProvincePanelLocal, gc);

        gc.gridy++;
        gc.gridx = 0;

        gc.insets = leftPaddingLocal;

        PaPanel companyPhonePanelLocal = new PaPanel();
        companyPhonePanelLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        companyPhonePanelLocal.add(createAlignedLabel(bundle.getString("lb_inv_phone") + ":"));
        companyPhoneLocal = new JTextField(invoicePreferenceSupplierLocal.getCompanyPhone());
        companyPhoneLocal.setColumns(10);
        companyPhonePanelLocal.add(companyPhoneLocal);
        ePanelLocal.add(companyPhonePanelLocal, gc);


        gc.gridx++;

        gc.insets = centerPaddingLocal;

        PaPanel companyEmailPanelLocal = new PaPanel();
        companyEmailPanelLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        companyEmailPanelLocal.add(createAlignedLabel(bundle.getString("lb_inv_email") + ":"));
        companyEmailLocal = new JTextField(invoicePreferenceSupplierLocal.getCompanyEmail());
        companyEmailLocal.setColumns(25);
        companyEmailPanelLocal.add(companyEmailLocal);
        ePanelLocal.add(companyEmailPanelLocal, gc);

        gc.gridy++;
        gc.gridx = 0;

        gc.insets = leftPadding;

        PaPanel supplierTaxPanelLocal = new PaPanel();
        supplierTaxPanelLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        supplierTaxPanelLocal.add(createAlignedLabel(bundle.getString("lb_inv_tax_id") + ":"));
        companyTaxIdLocal = new JTextField(invoicePreferenceSupplierLocal.getSupplierTaxId());
        companyTaxIdLocal.setColumns(20);
        supplierTaxPanelLocal.add(companyTaxIdLocal);
        ePanelLocal.add(supplierTaxPanelLocal, gc);


        gc.gridx++;

        gc.insets = centerPadding;

        PaPanel supplierTaxNamePanelLocal = new PaPanel();
        supplierTaxNamePanelLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        supplierTaxNamePanelLocal.add(createAlignedLabel(bundle.getString("lb_inv_tax_name") + ":"));
        companyTaxNameLocal = new JTextField(invoicePreferenceSupplierLocal.getSupplierTaxName());
        companyTaxNameLocal.setColumns(20);
        if (Global.country.equals("CAN")) {
            companyTaxNameLocal.setEditable(false);
        }
        supplierTaxNamePanelLocal.add(companyTaxNameLocal);
        ePanelLocal.add(supplierTaxNamePanelLocal, gc);

        gc.gridx++;

        gc.insets = rightPadding;

        PaPanel supplierTaxRatePanelLocal = new PaPanel();
        supplierTaxRatePanelLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        supplierTaxRatePanelLocal.add(createAlignedLabel(bundle.getString("lb_inv_tax_rate") + ":"));
        companyTaxRateLocal = new JTextField(String.valueOf(invoicePreferenceSupplierLocal.getSupplierTaxRate()));
        companyTaxRateLocal.setColumns(5);
        if (Global.country.equals("CAN")) {
            companyTaxRateLocal.setEditable(false);
        }
        supplierTaxRatePanelLocal.add(companyTaxRateLocal);
        supplierTaxRatePanelLocal.add(new JLabel("%"));
        ePanelLocal.add(supplierTaxRatePanelLocal, gc);


        // add a horizontal line
        gc.gridy++;
        gc.gridx = 0;
        gc.gridwidth = 4;
        gc.fill = GridBagConstraints.HORIZONTAL;
        gc.insets = new Insets(5, 0, 5, 0);
        ePanelLocal.add(new JSeparator(SwingConstants.HORIZONTAL), gc);
        gc.fill = GridBagConstraints.NONE;
        gc.gridwidth = 1;

        // Bill To

        gc.gridx = 0;
        gc.gridy++;

        gc.insets = leftPaddingLocal;

        PaPanel billToPanelLocal = new PaPanel();
        billToPanelLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        billToPanelLocal.add(createAlignedLabel(bundle.getString("lb_inv_bill_to") + ":"));
        billToNameFieldLocal = new JTextField(invoicePreferenceLocal.getBillToName());
        billToNameFieldLocal.setEditable(false);
        billToNameFieldLocal.setColumns(20);
        billToPanelLocal.add(billToNameFieldLocal);
        ePanelLocal.add(billToPanelLocal, gc);

        gc.gridy++;
        gc.gridx = 0;

        gc.insets = leftPaddingLocal;

        PaPanel billToAdd1Local = new PaPanel();
        billToAdd1Local.setLayout(new FlowLayout(FlowLayout.LEFT));
        billToAdd1Local.add(createAlignedLabel(bundle.getString("lb_inv_address1") + ":"));
        billToAddress1FieldLocal = new JTextField(invoicePreferenceLocal.getBillToAddress1());
        billToAddress1FieldLocal.setColumns(20);
        billToAddress1FieldLocal.setEditable(false);
        billToAdd1Local.add(billToAddress1FieldLocal);
        ePanelLocal.add(billToAdd1Local, gc);

        gc.gridx++;

        gc.insets = centerPaddingLocal;

        PaPanel billToAdd2Local = new PaPanel();
        billToAdd2Local.setLayout(new FlowLayout(FlowLayout.LEFT));
        billToAdd2Local.add(createAlignedLabel(bundle.getString("lb_inv_address2") + ":"));
        billToAddress2FieldLocal = new JTextField(invoicePreferenceLocal.getBillToAddress2());
        billToAddress2FieldLocal.setColumns(20);
        billToAddress2FieldLocal.setEditable(false);
        billToAdd2Local.add(billToAddress2FieldLocal);
        ePanelLocal.add(billToAdd2Local, gc);

        gc.gridy++;
        gc.gridx = 0;

        gc.insets = leftPaddingLocal;

        PaPanel billToCityLocal = new PaPanel();
        billToCityLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        billToCityLocal.add(createAlignedLabel(bundle.getString("lb_inv_city") + ":"));
        billToCityFieldLocal = new JTextField(invoicePreferenceLocal.getBillToCity());
        billToCityFieldLocal.setColumns(10);
        billToCityFieldLocal.setEditable(false);
        billToCityLocal.add(billToCityFieldLocal);
        ePanelLocal.add(billToCityLocal, gc);


        gc.gridx++;

        gc.insets = centerPaddingLocal;

        PaPanel billToZipLocal = new PaPanel();
        billToZipLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        billToZipLocal.add(createAlignedLabel(bundle.getString("lb_inv_zip") + ":"));
        billToZipFieldLocal = new JTextField(invoicePreferenceLocal.getBillToZip());
        billToZipFieldLocal.setColumns(5);
        billToZipFieldLocal.setEditable(false);
        billToZipLocal.add(billToZipFieldLocal);
        ePanelLocal.add(billToZipLocal, gc);

        gc.gridx++;

        gc.insets = rightPaddingLocal;

        PaPanel billToProvinceLocal = new PaPanel();
        billToProvinceLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        billToProvinceLocal.add(createAlignedLabel(bundle.getString("lb_inv_province") + ":"));
        billToStateComboLocal = new JComboBox<String>(new String[]{invoicePreferenceLocal.getBillToProvince()});
        billToStateComboLocal.setEditable(false);
        billToProvinceLocal.add(billToStateComboLocal);
        ePanelLocal.add(billToProvinceLocal, gc);

        // add a horizontal line
        gc.gridy++;
        gc.gridx = 0;
        gc.gridwidth = 4;
        gc.fill = GridBagConstraints.HORIZONTAL;
        gc.insets = new Insets(5, 0, 5, 0);
        ePanelLocal.add(new JSeparator(SwingConstants.HORIZONTAL), gc);
        gc.fill = GridBagConstraints.NONE;
        gc.gridwidth = 1;

        gc.gridx = 0;
        gc.gridy++;

        gc.insets = leftPaddingLocal;
        PaPanel shipToSameAsBillToPanelLocal = new PaPanel();
        shipToSameAsBillToPanelLocal.setLayout(new FlowLayout());
        shipToSameAsBillToLocal = new PaCheckBox();
        shipToSameAsBillToLocal.setText(bundle.getString("lb_inv_ship_to_same_as_bill_to"));
        shipToSameAsBillToPanelLocal.add(shipToSameAsBillToLocal);
        ePanelLocal.add(shipToSameAsBillToPanelLocal, gc);
        // add event listener
        shipToSameAsBillToLocal.addItemListener(e -> {
            if (e.getStateChange() == ItemEvent.SELECTED) {
                shipToNameFieldLocal.setText(billToNameFieldLocal.getText());
                shipToAddress1FieldLocal.setText(billToAddress1FieldLocal.getText());
                shipToAddress2FieldLocal.setText(billToAddress2FieldLocal.getText());
                shipToCityFieldLocal.setText(billToCityFieldLocal.getText());
                shipToZipFieldLocal.setText(billToZipFieldLocal.getText());
                shipToStateComboLocal.setSelectedItem(billToStateComboLocal.getSelectedItem());
                // Disable all ship to fields
                disableShipToFieldsLocal(true);

                // Force UI update
                SwingUtilities.invokeLater(() -> {
                    shipToNameFieldLocal.repaint();
                    shipToAddress1FieldLocal.repaint();
                    shipToAddress2FieldLocal.repaint();
                    shipToCityFieldLocal.repaint();
                    shipToZipFieldLocal.repaint();
                    shipToStateComboLocal.repaint();
                });
            } else {
                // Enable all ship to fields
                disableShipToFieldsLocal(false);

                // Force UI update
                SwingUtilities.invokeLater(() -> {
                    shipToNameFieldLocal.repaint();
                    shipToAddress1FieldLocal.repaint();
                    shipToAddress2FieldLocal.repaint();
                    shipToCityFieldLocal.repaint();
                    shipToZipFieldLocal.repaint();
                    shipToStateComboLocal.repaint();
                });
            }
        });


        // Ship To
        gc.gridx = 0;
        gc.gridy++;

        gc.insets = leftPaddingLocal;

        PaPanel shipToPanelLocal = new PaPanel();
        shipToPanelLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        shipToPanelLocal.add(createAlignedLabel(bundle.getString("lb_inv_ship_to") + ":"));
        shipToNameFieldLocal = new JTextField(invoicePreferenceLocal.getShipToName());
        shipToNameFieldLocal.setColumns(20);
        shipToPanelLocal.add(shipToNameFieldLocal);
        ePanelLocal.add(shipToPanelLocal, gc);

        gc.gridy++;
        gc.gridx = 0;

        gc.insets = leftPaddingLocal;

        PaPanel shipToAdd1Local = new PaPanel();
        shipToAdd1Local.setLayout(new FlowLayout(FlowLayout.LEFT));
        shipToAdd1Local.add(createAlignedLabel(bundle.getString("lb_inv_address1") + ":"));
        shipToAddress1FieldLocal = new JTextField(invoicePreferenceLocal.getShipToAddress1());
        shipToAddress1FieldLocal.setColumns(20);
        shipToAdd1Local.add(shipToAddress1FieldLocal);
        ePanelLocal.add(shipToAdd1Local, gc);

        gc.gridx++;

        gc.insets = centerPaddingLocal;

        PaPanel shipToAdd2Local = new PaPanel();
        shipToAdd2Local.setLayout(new FlowLayout(FlowLayout.LEFT));
        shipToAdd2Local.add(createAlignedLabel(bundle.getString("lb_inv_address2") + ":"));
        shipToAddress2FieldLocal = new JTextField(invoicePreferenceLocal.getShipToAddress2());
        shipToAddress2FieldLocal.setColumns(20);
        shipToAdd2Local.add(shipToAddress2FieldLocal);
        ePanelLocal.add(shipToAdd2Local, gc);

        gc.gridy++;
        gc.gridx = 0;

        gc.insets = leftPaddingLocal;

        PaPanel shipToCityLocal = new PaPanel();
        shipToCityLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        shipToCityLocal.add(createAlignedLabel(bundle.getString("lb_inv_city") + ":"));
        shipToCityFieldLocal = new JTextField(invoicePreferenceLocal.getShipToCity());
        shipToCityFieldLocal.setColumns(10);
        shipToCityLocal.add(shipToCityFieldLocal);
        ePanelLocal.add(shipToCityLocal, gc);

        gc.gridx++;

        gc.insets = centerPaddingLocal;

        PaPanel shipToZipLocal = new PaPanel();
        shipToZipLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        shipToZipLocal.add(createAlignedLabel(bundle.getString("lb_inv_zip") + ":"));
        shipToZipFieldLocal = new JTextField(invoicePreferenceLocal.getShipToZip());
        shipToZipFieldLocal.setColumns(5);
        shipToZipLocal.add(shipToZipFieldLocal);
        ePanelLocal.add(shipToZipLocal, gc);

        gc.gridx++;

        gc.insets = rightPaddingLocal;

        PaPanel shipToProvinceLocal = new PaPanel();
        shipToProvinceLocal.setLayout(new FlowLayout(FlowLayout.LEFT));
        shipToProvinceLocal.add(createAlignedLabel(bundle.getString("lb_inv_province") + ":"));
        shipToStateComboLocal = new JComboBox<String>();
        Global.provinces.forEach(shipToStateComboLocal::addItem);
        shipToProvinceLocal.add(shipToStateComboLocal);
        ePanelLocal.add(shipToProvinceLocal, gc);

        // buttons

        gc.gridx = 0;
        gc.gridy++;
        gc.insets = leftPaddingLocal;
        buttonPanelLocal.setLayout(new FlowLayout());
        saveButtonLocal = new JButton(bundle.getString("bt_update"));
        buttonPanelLocal.add(saveButtonLocal, BorderLayout.WEST);

        saveButtonLocal.addActionListener(e -> {
            String error = validateDataLocal();
            if (error != null) {
                JOptionPane.showMessageDialog(this, error);
                return;
            }
            useLocalPreference();
        });

        ePanelLocal.add(buttonPanelLocal, gc);
        ///////

        setPref();
        Dimension dimension = new Dimension(1000, 700);
        setSize(dimension);
        setMinimumSize(dimension);
        setLocationRelativeTo(parent);
        setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
    }

    private void updatePreference() {
        var supplier = new InvoicePreferenceSupplier();
        supplier.setCompanyName(companyNameField.getText());
        supplier.setCompanyAddress1(companyAddress1Field.getText());
        supplier.setCompanyAddress2(companyAddress2Field.getText());
        supplier.setCompanyCity(companyCityField.getText());
        supplier.setCompanyProvince((String) companyStateCombo.getSelectedItem());
        supplier.setCompanyZip(companyZipField.getText());
        supplier.setCompanyPhone(companyPhone.getText());
        supplier.setCompanyEmail(companyEmail.getText());
        supplier.setSupplierTaxId(companyTaxId.getText());
        supplier.setSupplierTaxRate(Double.parseDouble(companyTaxRate.getText()));
        supplier.setSupplierTaxName(companyTaxName.getText());
        var invoicePreferenceClient = new InvoicePreferenceClient();
        invoicePreferenceClient.setBillToName(billToNameField.getText());
        invoicePreferenceClient.setBillToAddress1(billToAddress1Field.getText());
        invoicePreferenceClient.setBillToAddress2(billToAddress2Field.getText());
        invoicePreferenceClient.setBillToCity(billToCityField.getText());
        invoicePreferenceClient.setBillToProvince((String) billToStateCombo.getSelectedItem());
        invoicePreferenceClient.setBillToZip(billToZipField.getText());
        invoicePreferenceClient.setShipToName(shipToNameField.getText());
        invoicePreferenceClient.setShipToAddress1(shipToAddress1Field.getText());
        invoicePreferenceClient.setShipToAddress2(shipToAddress2Field.getText());
        invoicePreferenceClient.setShipToCity(shipToCityField.getText());
        invoicePreferenceClient.setShipToProvince((String) shipToStateCombo.getSelectedItem());
        invoicePreferenceClient.setShipToZip(shipToZipField.getText());
        invoicePreferenceListener.setInvoicePreferenceClient(invoicePreferenceClient);
        invoicePreferenceListener.setInvoicePreferenceSupplier(supplier);
        JOptionPane.showMessageDialog(this, "Invoice Preference Updated successfully");
        this.dispose();
    }

    private void useLocalPreference() {
        var supplier = new InvoicePreferenceSupplier();
        supplier.setCompanyName(companyNameFieldLocal.getText());
        supplier.setCompanyAddress1(companyAddress1FieldLocal.getText());
        supplier.setCompanyAddress2(companyAddress2FieldLocal.getText());
        supplier.setCompanyCity(companyCityFieldLocal.getText());
        supplier.setCompanyProvince((String) companyStateComboLocal.getSelectedItem());
        supplier.setCompanyZip(companyZipFieldLocal.getText());
        supplier.setCompanyPhone(companyPhoneLocal.getText());
        supplier.setCompanyEmail(companyEmailLocal.getText());
        supplier.setSupplierTaxId(companyTaxIdLocal.getText());
        supplier.setSupplierTaxRate(Double.parseDouble(companyTaxRateLocal.getText()));
        supplier.setSupplierTaxName(companyTaxNameLocal.getText());
        var invoicePreferenceClient = new InvoicePreferenceClient();
        invoicePreferenceClient.setBillToName(billToNameFieldLocal.getText());
        invoicePreferenceClient.setBillToAddress1(billToAddress1FieldLocal.getText());
        invoicePreferenceClient.setBillToAddress2(billToAddress2FieldLocal.getText());
        invoicePreferenceClient.setBillToCity(billToCityFieldLocal.getText());
        invoicePreferenceClient.setBillToProvince((String) billToStateComboLocal.getSelectedItem());
        invoicePreferenceClient.setBillToZip(billToZipFieldLocal.getText());
        invoicePreferenceClient.setShipToName(shipToNameFieldLocal.getText());
        invoicePreferenceClient.setShipToAddress1(shipToAddress1FieldLocal.getText());
        invoicePreferenceClient.setShipToAddress2(shipToAddress2FieldLocal.getText());
        invoicePreferenceClient.setShipToCity(shipToCityFieldLocal.getText());
        invoicePreferenceClient.setShipToProvince((String) shipToStateComboLocal.getSelectedItem());
        invoicePreferenceClient.setShipToZip(shipToZipFieldLocal.getText());
        invoicePreferenceListener.setInvoicePreferenceClient(invoicePreferenceClient);
        invoicePreferenceListener.setInvoicePreferenceSupplier(supplier);
        JOptionPane.showMessageDialog(this, "Invoice Preference Updated successfully");
        this.dispose();
    }

    public void setPref() {
        // Set the appropriate supplier in the dropdown
        if (suppliers.isEmpty()) {
            // If there are no suppliers in the list, select the "New Supplier..." option
            if (supplierComboBox.getItemCount() > 0) {
                // This will trigger the action listener which will create a new supplier
                supplierComboBox.setSelectedIndex(supplierComboBox.getItemCount() - 1);
            }

            // Clear all fields since we're creating a new supplier
            clearSupplierFields();

            // Set default to false for new supplier
            defaultSupplierCheckbox.setSelected(false);

            // Initialize tax fields based on the selected province
            setTaxFields();
        } else {
            // Select the first supplier or preselect the current supplier
            if (supplierComboBox.getItemCount() > 1) { // At least one supplier + New Supplier option
                // preselect the current supplier
                if (invoicePreferenceSupplier != null) {
                    int index = suppliers.indexOf(invoicePreferenceSupplier);
                    if (index != -1) {
                        supplierComboBox.setSelectedIndex(index);
                    } else {
                        supplierComboBox.setSelectedIndex(0);
                    }
                } else {
                    supplierComboBox.setSelectedIndex(0);
                }

            }

            // Update fields with the selected supplier's data
            updateSupplierFields();
        }

        // Set bill-to and ship-to fields
        billToStateCombo.setSelectedItem(invoicePreference.getClient().getBillToProvince());
        shipToStateCombo.setSelectedItem(invoicePreference.getClient().getShipToProvince());

        // Set local preference fields
        companyStateComboLocal.setSelectedItem(invoicePreferenceSupplierLocal.getCompanyProvince());
        billToStateComboLocal.setSelectedItem(invoicePreferenceLocal.getBillToProvince());
        shipToStateComboLocal.setSelectedItem(invoicePreferenceLocal.getShipToProvince());
        setTaxFieldsLocal();
    }

    private void setTaxFields() {
        Pair<String, Double> taxes = Utils.getTaxes(String.valueOf(companyStateCombo.getSelectedItem()));
        if (Global.country.equals("CAN")) {
            companyTaxName.setText(taxes.getKey());
            companyTaxRate.setText(String.valueOf(taxes.getValue()));
        } else {
            if (invoicePreferenceSupplier != null) {
                companyTaxName.setText(invoicePreferenceSupplier.getSupplierTaxName());
                companyTaxRate.setText(String.valueOf(invoicePreferenceSupplier.getSupplierTaxRate()));
            } else {
                companyTaxName.setText("");
                companyTaxRate.setText("0.0");
            }
        }
    }

    private void setTaxFieldsLocal() {
        Pair<String, Double> taxesLocal = Utils.getTaxes(String.valueOf(companyStateComboLocal.getSelectedItem()));
        if (Global.country.equals("CAN")) {
            companyTaxNameLocal.setText(taxesLocal.getKey());
            companyTaxRateLocal.setText(String.valueOf(taxesLocal.getValue()));
        } else {
            companyTaxNameLocal.setText(invoicePreferenceSupplierLocal.getSupplierTaxName());
            companyTaxRateLocal.setText(String.valueOf(invoicePreferenceSupplierLocal.getSupplierTaxRate()));
        }
    }

    /**
     * Updates all supplier-related fields with the currently selected supplier's data
     */
    private void updateSupplierFields() {
        if (invoicePreferenceSupplier == null) {
            // If supplier is null, clear all fields
            clearSupplierFields();
            defaultSupplierCheckbox.setSelected(false);
            return;
        }

        companyNameField.setText(invoicePreferenceSupplier.getCompanyName());
        companyAddress1Field.setText(invoicePreferenceSupplier.getCompanyAddress1());
        companyAddress2Field.setText(invoicePreferenceSupplier.getCompanyAddress2());
        companyCityField.setText(invoicePreferenceSupplier.getCompanyCity());
        companyZipField.setText(invoicePreferenceSupplier.getCompanyZip());
        companyPhone.setText(invoicePreferenceSupplier.getCompanyPhone());
        companyEmail.setText(invoicePreferenceSupplier.getCompanyEmail());
        companyTaxId.setText(invoicePreferenceSupplier.getSupplierTaxId());

        // Set the province/state and update tax fields
        if (invoicePreferenceSupplier.getCompanyProvince() != null) {
            companyStateCombo.setSelectedItem(invoicePreferenceSupplier.getCompanyProvince());
        }

        // Update the default checkbox
        defaultSupplierCheckbox.setSelected(invoicePreferenceSupplier.getIsDefault());

        // Tax fields will be updated by the setTaxFields method called from the companyStateCombo listener
    }


    /**
     * Clears all supplier-related fields for a new supplier
     */
    private void clearSupplierFields() {
        companyNameField.setText("");
        companyAddress1Field.setText("");
        companyAddress2Field.setText("");
        companyCityField.setText("");
        companyZipField.setText("");
        companyPhone.setText("");
        companyEmail.setText("");
        companyTaxId.setText("");
        // Note: We don't clear tax name and rate fields here
        // They will be set by setTaxFields() based on the selected province

        // Set focus to the company name field for convenience
        companyNameField.requestFocus();
    }

    /**
     * Creates a JLabel with consistent width for alignment purposes
     *
     * @param text The label text
     * @return A JLabel with consistent width
     */
    private JLabel createAlignedLabel(String text) {
        JLabel label = new JLabel(text);
        label.setPreferredSize(new Dimension(100, label.getPreferredSize().height));
        label.setHorizontalAlignment(SwingConstants.RIGHT);
        return label;
    }

    /**
     * Disables or enables all ship to fields in the company preferences tab
     *
     * @param disable true to disable, false to enable
     */
    private void disableShipToFields(boolean disable) {
        shipToNameField.setEditable(!disable);
        shipToAddress1Field.setEditable(!disable);
        shipToAddress2Field.setEditable(!disable);
        shipToCityField.setEditable(!disable);
        shipToZipField.setEditable(!disable);
        shipToStateCombo.setEditable(!disable);
    }

    /**
     * Disables or enables all ship to fields in the local preferences tab
     *
     * @param disable true to disable, false to enable
     */
    private void disableShipToFieldsLocal(boolean disable) {
        shipToNameFieldLocal.setEditable(!disable);
        shipToAddress1FieldLocal.setEditable(!disable);
        shipToAddress2FieldLocal.setEditable(!disable);
        shipToCityFieldLocal.setEditable(!disable);
        shipToZipFieldLocal.setEditable(!disable);
        shipToStateComboLocal.setEditable(!disable);
    }

    /**
     * Validates only the supplier data
     */
    private String validateSupplierData() {
        // Make sure we have a supplier object
        if (invoicePreferenceSupplier == null) {
            invoicePreferenceSupplier = new InvoicePreferenceSupplier();
        }
        if (companyNameField.getText().isBlank()) {
            return "Company Name is required";
        }
        if (companyAddress1Field.getText().isBlank()) {
            return "Company Address1 is required";
        }
        if (companyCityField.getText().isBlank()) {
            return "Company City is required";
        }
        if (companyZipField.getText().isBlank()) {
            return "Company Zip is required";
        }
        if (companyPhone.getText().isBlank()) {
            return "Company Phone is required";
        }
        if (companyEmail.getText().isBlank()) {
            return "Company Email is required";
        }

        // Validate email format
        String email = companyEmail.getText();
        if (!Utils.isValidEmail(email)) {
            return bundle.getString("msg_invalid_email");
        }
        if (!companyTaxRate.getText().isBlank()) {
            try {
                double rate = Double.parseDouble(companyTaxRate.getText());
                if (rate < 0 || rate > 100) {
                    return "Invalid Tax Rate";
                }
                if (companyTaxName.getText().isBlank()) {
                    return "Tax Name is required";
                }
            } catch (NumberFormatException e) {
                return "Invalid Tax Rate";
            }
        }
        return null;
    }

    /**
     * Validates only the bill-to and ship-to data
     */
    private String validateBillShipData() {
        if (billToNameField.getText().isBlank()) {
            return "Bill To Name is required";
        }
        if (billToAddress1Field.getText().isBlank()) {
            return "Bill To Address1 is required";
        }
        if (billToCityField.getText().isBlank()) {
            return "Bill To City is required";
        }
        if (billToZipField.getText().isBlank()) {
            return "Bill To Zip is required";
        }
        if (shipToNameField.getText().isBlank()) {
            return "Ship To Name is required";
        }
        if (shipToAddress1Field.getText().isBlank()) {
            return "Ship To Address1 is required";
        }
        if (shipToCityField.getText().isBlank()) {
            return "Ship To City is required";
        }
        if (shipToZipField.getText().isBlank()) {
            return "Ship To Zip is required";
        }
        return null;
    }

    /**
     * Validates all data (supplier, bill-to, and ship-to)
     */
    private String validateData() {
        String supplierError = validateSupplierData();
        if (supplierError != null) {
            return supplierError;
        }

        String billShipError = validateBillShipData();
        if (billShipError != null) {
            return billShipError;
        }

        return null;
    }

    private String validateDataLocal() {
        if (companyNameFieldLocal.getText().isBlank()) {
            return "Company Name is required";
        }
        if (companyAddress1FieldLocal.getText().isBlank()) {
            return "Company Address1 is required";
        }
        if (companyCityFieldLocal.getText().isBlank()) {
            return "Company City is required";
        }
        if (companyZipFieldLocal.getText().isBlank()) {
            return "Company Zip is required";
        }
        if (companyPhoneLocal.getText().isBlank()) {
            return "Company Phone is required";
        }
        if (companyEmailLocal.getText().isBlank()) {
            return "Company Email is required";
        }

        // Validate email format
        String email = companyEmailLocal.getText();
        if (!Utils.isValidEmail(email)) {
            return bundle.getString("msg_invalid_email");
        }
        if (!companyTaxRateLocal.getText().isBlank()) {
            try {
                Double rate = Double.parseDouble(companyTaxRateLocal.getText());
                if (rate < 0 || rate > 100) {
                    return "Invalid Tax Rate";
                }
                if (companyTaxNameLocal.getText().isBlank()) {
                    return "Tax Name is required";
                }
            } catch (NumberFormatException e) {
                return "Invalid Tax Rate";
            }
        }
        if (billToNameFieldLocal.getText().isBlank()) {
            return "Bill To Name is required";
        }
        if (billToAddress1FieldLocal.getText().isBlank()) {
            return "Bill To Address1 is required";
        }
        if (billToCityFieldLocal.getText().isBlank()) {
            return "Bill To City is required";
        }
        if (billToZipFieldLocal.getText().isBlank()) {
            return "Bill To Zip is required";
        }
        if (shipToNameFieldLocal.getText().isBlank()) {
            return "Ship To Name is required";
        }
        if (shipToAddress1FieldLocal.getText().isBlank()) {
            return "Ship To Address1 is required";
        }
        if (shipToCityFieldLocal.getText().isBlank()) {
            return "Ship To City is required";
        }
        if (shipToZipFieldLocal.getText().isBlank()) {
            return "Ship To Zip is required";
        }
        return null;
    }
}
