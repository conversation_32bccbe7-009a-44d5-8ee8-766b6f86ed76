package com.partskick.gui;

import com.partskick.gui.common.ResourceFactory;
import com.partskick.model.Estimation;

import javax.swing.table.DefaultTableModel;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;

public class EstimationTableModel extends DefaultTableModel {
	
	private List<Estimation> db = new ArrayList<>();
	
	private String[] colNames = new String[3];
	
	public EstimationTableModel() {
		super();
		ResourceBundle bundle = ResourceFactory.getBundle();

		colNames[0] = bundle.getString("lb_vin_insurance");
		colNames[1] = bundle.getString("lb_vehicle_info");
		colNames[2] = bundle.getString("lb_ems");
	}

	@Override
	public boolean isCellEditable(int row, int column) {
		//all cells false
		return false;
	}
	
	@Override
	public String getColumnName(int column) {
		// TODO Auto-generated method stub
		return colNames[column];
	}


	public void setData(List<Estimation> db) {
		this.db = db;
		fireTableDataChanged();
	}

	@Override
	public int getColumnCount() {
		return 3;
	}

	@Override
	public int getRowCount() {
		return db == null ? 0 : db.size();
	}

	@Override
	public Object getValueAt(int row, int col) {
		Estimation est = db.get(row);
		
		switch(col) {
		case 0:
			return est.getVinInsuClm();
		case 1:
			return est.getYMMCLFn();
		case 2:
			return est.getEmsRo();
		}
		
		return null;
	}

	public Estimation getEMS(int row) {
		return db.get(row);
	}
}
