package com.partskick.gui;

import com.partskick.Global;
import com.partskick.PkVersion;
import com.partskick.Utils;
import com.partskick.client.ClientConfig;
import com.partskick.client.ClientHelper;
import com.partskick.client.ClientV3;
import com.partskick.client.MessageSettings;
import com.partskick.common.License;
import com.partskick.common.VerUpgrade;
import com.partskick.gui.common.DialogFactory;
import com.partskick.gui.common.ResourceFactory;
import com.partskick.gui.invoice.InvoiceHistoryDialog;
import com.partskick.gui.invoice.InvoiceSettingsDialog;
import com.partskick.gui.message.MessagePopupDialog;
import com.partskick.gui.message.MessageScheduler;
import com.partskick.gui.prefs.PrefsDialog;
import com.partskick.gui.prefs.PrefsListener;
import com.partskick.gui.progress.ProgressDialog;
import com.partskick.gui.progress.ProgressIndeterminateDialog;
import com.partskick.gui.register.RegistDialog;
import com.partskick.gui.register.RegistListener;
import com.partskick.parts.*;
import com.partskick.service.EmsFileService;
import com.partskick.service.HttpService;
import com.partskick.service.PropertyLoader;
import com.partskick.ui.EmsSource;
import com.partskick.ui.EmsSourceHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import oshi.PlatformEnum;
import oshi.SystemInfo;
import oshi.hardware.HardwareAbstractionLayer;

import javax.swing.*;
import javax.swing.event.HyperlinkEvent;
import javax.swing.event.HyperlinkListener;
import java.awt.*;
import java.awt.event.*;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.prefs.BackingStoreException;
import java.util.prefs.Preferences;
import java.util.regex.Pattern;

import static com.partskick.gui.invoice.InvoicePreferenceConstants.DEFAULT_INVOICE_PREFERENCE_CLIENT;

@Slf4j
public class MainFrame extends JFrame {

	private static final long serialVersionUID = -620793828379526809L;
	private EmsToolbar emsToolbar;
	private PrefsDialog prefsDialog;
	private RegistDialog registDialog;
	private Preferences prefs;
	private Preferences regInfo;
	private EstimatePanel estPanel;
	private HttpService httpService;
	private List<PartsType> partsTypes = new ArrayList<>();
	private Map<String, String> altTypeReplacement = new HashMap<>();
	private Map<String, String> partsNumReplacement = new HashMap<>();
	private List<EmsSource> emsSources;

	private ProgressDialog progressDialog;
	private ProgressIndeterminateDialog progressIndeterminateDialog;

	private Integer removeEmsDays = 0;
	private Integer removeProcessedDays = 0;

	private Double overwriteDisc = 0.0;
	private ResourceBundle bundle;
	private EmsFileService emsFileService;
	private PropertyLoader propertyLoader;

	private MessageSettings messageSettings;
	private LocalDateTime stopMessageAt;
	private MessageScheduler messageScheduler;

	public MainFrame(String[] args) throws SQLException {

		super();

		boolean useLocalMachineReg = false;
		// search args for registry-local-machine=true
		String param = Utils.findParameter(args, "registry-local-machine");

		if (param != null) {
			useLocalMachineReg = Boolean.parseBoolean(param);
		}

		// Set the global installation type for other classes to use
		Utils.setInstallationType(useLocalMachineReg);

		// Perform preference migration if needed (before any other initialization)
		// Use the same installation type for migration as for preferences
		Utils.migratePreferencesFromOldLocation(useLocalMachineReg);

		if (useLocalMachineReg) {
			prefs = Utils.getPartsKickPrefs(true);  // System-wide (HKLM)
			regInfo = Utils.getPartsKickRegPrefs(true);  // System-wide (HKLM)
		} else {
			prefs = Utils.getPartsKickPrefs(false);  // User-specific (HKCU)
			regInfo = Utils.getPartsKickRegPrefs(false);  // User-specific (HKCU)
		}

		propertyLoader = new PropertyLoader();
		bundle = ResourceBundle.getBundle("com.partskick.gui.resources.MessageResource");
		ResourceFactory.setBundle(bundle);

		progressDialog = new ProgressDialog(this, bundle.getString("ms_load"));
		DialogFactory.setProgressDialog(progressDialog);
		progressIndeterminateDialog = new ProgressIndeterminateDialog(this, bundle.getString("ms_process"));
		DialogFactory.setProgressIndeterminateDialog(progressIndeterminateDialog);

		httpService = new HttpService();
		Global.httpService = httpService;

		ActionListener exitListener = new ActionListener() {
			public void actionPerformed(ActionEvent arg0) {

				int action = JOptionPane.showConfirmDialog(MainFrame.this,
						bundle.getString("ms_exit_confirm"), bundle.getString("tt_exit_confirm"),
						JOptionPane.OK_CANCEL_OPTION);

				if (action == JOptionPane.OK_OPTION) {
					httpService.audit(Global.clientId, Global.workstation, Global.machineId, "LOGOUT", "close window");

					// Shutdown message scheduler if it exists
					if (messageScheduler != null) {
						messageScheduler.shutdown();
					}
					
					dispose();
					System.gc();
					System.exit(0);
				}
			}
		};


		setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
		addWindowListener(new WindowAdapter() {
			public void windowClosing(WindowEvent arg0) {
				exitListener.actionPerformed(null);
			}
		});

		log.info("Initializing System...");
		SystemInfo si = new SystemInfo();

		if (si.getCurrentPlatform().equals(PlatformEnum.UNKNOWN)) {
			JOptionPane.showMessageDialog(null, si.getCurrentPlatform() + bundle.getString("ms_not_supported"));
			System.exit(1);
		}

		HardwareAbstractionLayer hal = si.getHardware();
		String v = hal.getComputerSystem().getHardwareUUID();
		StringBuilder sb = new StringBuilder(StringUtils.isNotBlank(v) ? v : "UN");
//		log.info("HardWare UUID: " + hal.getComputerSystem().getHardwareUUID());
		v = Utils.extract(hal.getComputerSystem().toString(), "serial number", "=");
		sb.append("||").append(StringUtils.isNotBlank(v) ? v : "UN");
//		log.info("PC SN: " + Utils.extract(hal.getComputerSystem().toString(), "serial number", "="));
		v = Utils.extract(hal.getComputerSystem().getBaseboard().toString(), "serial number", "=");
		sb.append("||").append(StringUtils.isNotBlank(v) ? v : "UN");
//		log.info("PC baseboard SN: " + Utils.extract(hal.getComputerSystem().getBaseboard().toString(), "serial number", "="));
		v = Utils.extract(hal.getProcessor().toString(), "ProcessorID", ":");
		sb.append("||").append(StringUtils.isNotBlank(v) ? v : "UN");
//		log.info("PC processor ID: " + Utils.extract(hal.getProcessor().toString(), "ProcessorID", ":"));
//		log.info(sb.toString());
//		log.info("sha3_256Hex: " +  DigestUtils.sha3_256Hex(sb.toString()));

		registDialog = new RegistDialog(this, bundle.getString("lb_registration"), sb.toString(), httpService);

		String clientId = regInfo.get(RegistDialog.CLIENT, "");
		Global.clientId = clientId;
		String wks = regInfo.get(RegistDialog.STATION, Utils.getHostname());
		Global.workstation = wks;
		Global.machineId = sb.toString();


		////////////////  validate license /////////////////////////////////
		Long submitTime = System.currentTimeMillis();
		boolean showRegWindow = false;
		if (StringUtils.isBlank(clientId) || StringUtils.isBlank(wks)) {
			showRegWindow = true;
		} else {
			Pair<Integer, String> ck = httpService.validateStatus(clientId, wks, sb.toString(), submitTime);
			if (ck != null && ck.getLeft() != null && ck.getLeft().equals(License.exception.getStatus())) {
				if (StringUtils.isNotBlank(ck.getRight())) {
					JOptionPane.showMessageDialog(null, Utils.trimString(ck.getRight()));
				} else {
					JOptionPane.showMessageDialog(null, bundle.getString("ms_something_wrong") + "\r\n" + bundle.getString("ms_check_network"));
				}
				System.exit(1);
			}
			if (ck == null || ck.getLeft() == null || !ck.getLeft().equals(License.licensed.getStatus())) {
				if (ck != null && StringUtils.isNotBlank(ck.getRight())) {
					JOptionPane.showMessageDialog(null, Utils.trimString(ck.getRight()));
				}
				showRegWindow = true;
			}
			if (StringUtils.isNotBlank(ck.getRight())) {
				registDialog.setLicenseInfo(ck.getRight());
			}
		}

		if (StringUtils.isBlank(clientId)) {
			clientId = Utils.getUUIDFromClipboard();
		}
		registDialog.setRegistListener(new RegistListener() {
			@Override
			public void registSet(String client, String station) {
				boolean clientSuccess = Utils.putPreferenceWithAdminCheck(RegistDialog.CLIENT, client, MainFrame.this, bundle);
				boolean stationSuccess = Utils.putPreferenceWithAdminCheck(RegistDialog.STATION, station, MainFrame.this, bundle);
				
				if (!clientSuccess || !stationSuccess) {
					log.warn("Failed to save registration settings - client: {}, station: {}", client, station);
				}
				
				Global.clientId = client;
				Global.workstation = station;
			}
		});
		registDialog.setDefaults(clientId, wks);



		if (showRegWindow) {
			registDialog.setExit(true);
			registDialog.setVisible(true);
		}


		////////////////  validate license -- end /////////////////////////////////

		////////////////  retrieve configurations from server /////////////////////////////////

		try {

//			ClientHelper.setIfRemovePrivacy(httpService.ifRemovePrivacy(clientId, wks));

			ClientConfig config = httpService.getConfig(clientId, Global.machineId, wks);
			messageSettings = config.getMessageSettings();
			Global.enableDebug = config.isEnableDebug();
			Global.workstationId = config.getWsId();
			Global.provinces = config.getProvinces();
            Global.country = config.getCountry();

			VerUpgrade vu = httpService.getVersionUpgrade(clientId, wks);
			if (vu.getForceUpgrade()) {

				JLabel label = new JLabel();
				Font font = label.getFont();

				// create some css from the label's font
				StringBuffer style = new StringBuffer("font-family:" + font.getFamily() + ";");
				style.append("font-weight:" + (font.isBold() ? "bold" : "normal") + ";");
				style.append("font-size:" + font.getSize() + "pt;");

				// html content
				JEditorPane ep = new JEditorPane("text/html", "<html><body style=\"" + style + "\">" //
						+ bundle.getString("ms_force_upgrade") + " <a href=\"" + vu.getDownloadUrl() + "\">"
						+ bundle.getString("ms_force_upgrade_click") + "</a> " + bundle.getString("ms_force_upgrade_download") //
						+ "</body></html>");

				// handle link events
				ep.addHyperlinkListener(new HyperlinkListener()
				{
					@Override
					public void hyperlinkUpdate(HyperlinkEvent e)
					{
						if (e.getEventType().equals(HyperlinkEvent.EventType.ACTIVATED)) {
							try {
								Desktop.getDesktop().browse(e.getURL().toURI());
							} catch (IOException ex) {
								throw new RuntimeException(ex);
							} catch (URISyntaxException ex) {
								throw new RuntimeException(ex);
							}
						}
					}
				});
				ep.setEditable(false);
				ep.setBackground(label.getBackground());


				JOptionPane.showMessageDialog(null, ep);
				System.exit(0);
			}

			PartsConfig partsConfig = httpService.getPartsConfig(clientId, sb.toString());
			Double defaultShopDiscount = partsConfig.getDefaultShopDiscount();
			if (defaultShopDiscount != null && defaultShopDiscount > 0.0) {
				Global.defaultShopDiscount = defaultShopDiscount;
			}
			migrateSources(partsConfig.getEmsSources(), partsConfig.getOldEmsSources());
			emsSources = partsConfig.getEmsSources();
			if (emsSources == null) emsSources = new ArrayList<>();

			List<PartsReplaceType> partsRepl = partsConfig.getPartsReplaceTypes();

			for (PartsReplaceType p : partsRepl) {
				if (p.getReplaceType() == 1) {
					partsNumReplacement.put(p.getReplaceParts(), p.getReplaceWith());
				} else if (p.getReplaceType() == 2) {
					altTypeReplacement.put(p.getReplaceParts(), p.getReplaceWith());
				}
			}


			List<PartsType> partsTypeList = partsConfig.getPartsTypes();
			if (partsTypeList != null) {
				for (PartsType pt : partsTypeList) {
					if (pt.isExcludeTarget()) {
						PartsTypeHelper.setMarkRemovedType(pt.getType());
					}
					if (pt.isExcluded()) {
						Set<String> excluded = PartsTypeHelper.getExcludedTypes();
						if (excluded == null) {
							excluded = new HashSet<>();
							PartsTypeHelper.setExcludedTypes(excluded);
						}
						excluded.add(pt.getType());

					} else {
						partsTypes.add(pt);
						if (pt.isTarget()) {
							PartsTypeHelper.setTargetType(pt);
						}
					}
				}
			}
			PartsTypeHelper.initMap(partsTypes);
			PartsTypeHelper.setAltPartsTypeReplacement(altTypeReplacement);
			PartsTypeHelper.setPartsNumReplacement(partsNumReplacement);

			PartsTypeHelper.setTieredDiscs(partsConfig.getPartsTypeTieredDiscs().stream().toList());

			PartsTypeHelper.setShopDiscs(partsConfig.getPartsTypeShopDiscs().stream().toList());
			for (PartsAdjOperation op : partsConfig.getPartsAdjOperations()) {
				Global.processTypes.add(Pair.of(Pattern.compile(op.getDescMatchPattern(), Pattern.CASE_INSENSITIVE), op.getProcessType()));
			}
			ClientHelper.setClient(httpService.getClientInfoV3(clientId, sb.toString()));
			Global.configMap = httpService.getGlobalConfig(clientId, sb.toString());
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			JOptionPane.showMessageDialog(null, bundle.getString("ms_something_wrong"));
			System.exit(1);
		}

		try {
			Global.companyInvoicePreference = Global.httpService.getInvoicePreference(Global.clientId, Global.machineId);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			Global.companyInvoicePreference = new InvoicePreferenceV2();
		}
		try {
			Global.taxes = Global.httpService.getTaxes(Global.clientId, Global.machineId);
			Global.clientProvinceTaxes = Global.taxes.stream().filter(t -> t.getProvinceState().equals(ClientHelper.getClient().getProvince())).toList();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			Global.taxes = new ArrayList<>();
		}

		////////////////  retrieve configurations from server -- end /////////////////////////////////

		////////////////  retrieve configurations from local /////////////////////////////////
		Long msa = prefs.getLong(PrefsDialog.MESSAGE_STOPPED_AT, 0);
		if (msa > 0) {
			stopMessageAt = LocalDateTime.ofInstant(Instant.ofEpochMilli(msa), ZoneId.systemDefault());
		}

		for (EmsSource emsSource: emsSources) {
			emsSource.setExportPath(prefs.get(emsSource.getName() + PrefsDialog.EXPORT_PATH, ""));
			emsSource.setTraderPath(prefs.get(emsSource.getName() + PrefsDialog.TRADER_PATH, ""));
		}
		EmsSourceHelper.init(emsSources);

		int searchScope = prefs.getInt(PrefsDialog.PART_SEARCH_SCOPE, -1);
		if (searchScope > -1 && ClientHelper.getClient() instanceof ClientV3 clientV3) {
			clientV3.setPartsSearchScope(searchScope);
		}

		boolean autoPush = prefs.getBoolean(PrefsDialog.AUTO_PUSH, false);
		boolean fixMtoC = prefs.getBoolean(PrefsDialog.FIX_M_C, false);
		removeEmsDays = prefs.getInt(PrefsDialog.AUTO_REMOVE_SOURCE, 1);
		if (removeEmsDays < 0) removeEmsDays = 1;
		removeProcessedDays = prefs.getInt(PrefsDialog.AUTO_REMOVE_TARGET, 1);
		if (removeProcessedDays < 0) removeProcessedDays = 1;
		overwriteDisc = prefs.getDouble(PrefsDialog.OVERWRITE_DISCOUNT, Global.defaultShopDiscount);
		if (overwriteDisc > 0.0 && !Objects.equals(overwriteDisc, Global.defaultShopDiscount)) {
			Global.defaultShopDiscount = overwriteDisc;
		} else {
			overwriteDisc = Global.defaultShopDiscount;
		}
		boolean manualTarget = prefs.getBoolean(PrefsDialog.MANUAL_TARGET, false);
		int loadPeriod = prefs.getInt(PrefsDialog.LOAD_PERIOD, 1);
		int loadNumber = prefs.getInt(PrefsDialog.LOAD_NUMBER, 0);

		PartsTypeHelper.getTypes().stream().forEach(t -> {
			if (t.isAllowKeepType()) {
				if (t.isEnforce()) {
					boolean success = Utils.putBooleanPreferenceWithAdminCheck(PrefsDialog.PARTS_TYPE_PREFIX + t.getId(), t.isKeepType(), this, bundle);
					if (!success) {
						log.warn("Failed to save parts type preference: {} = {}", t.getId(), t.isKeepType());
					}
				} else {
					t.setKeepType(prefs.getBoolean(PrefsDialog.PARTS_TYPE_PREFIX + t.getId(), t.isKeepType()));
				}
			}
		});

		Utils.loadLocalInvoicePreferences(prefs);
		Global.useClientReferenceAsDefault = prefs.getBoolean(DEFAULT_INVOICE_PREFERENCE_CLIENT, true);
		////////////////  retrieve configurations from local -- end /////////////////////////////////
		String fixEnv = propertyLoader.getProperties().getProperty("pa.export.fix.env");
		if (StringUtils.isNotBlank(fixEnv)) {
			Global.fixMtoCType = fixEnv;
		}

		param = Utils.findParameter(args, "fix-m-c");
		if (param != null) {
			Global.fixMtoCType = param;
		}
		// get
		Global.loadPeriod = loadPeriod;
		Global.loadNumber = loadNumber;
		Global.fixMtoC = fixMtoC;
		prefsDialog = new PrefsDialog(this, bundle.getString("tt_prefs"));
		prefsDialog.setDefaults(emsSources, autoPush, removeEmsDays, removeProcessedDays, manualTarget, loadPeriod, loadNumber, overwriteDisc, fixMtoC);
		prefsDialog.setPrefsListener(new PrefsListener() {
			public void preferencesSet(boolean autoPush, int autoDeleteSourceDays, int autoDeleteTargetDays, boolean manualTgt, int loadPeriod, int loadNumber, Double overwriteDisc, boolean fixMC) {
				for (EmsSource source : emsSources) {
					boolean exportSuccess = Utils.putPreferenceWithAdminCheck(source.getName() + PrefsDialog.EXPORT_PATH, source.getExportPath(), MainFrame.this, bundle);
					boolean traderSuccess = Utils.putPreferenceWithAdminCheck(source.getName() + PrefsDialog.TRADER_PATH, source.getTraderPath(), MainFrame.this, bundle);
					if (!exportSuccess || !traderSuccess) {
						log.warn("Failed to save EMS source paths for: {}", source.getName());
					}
				}
				
				boolean autoPushSuccess = Utils.putBooleanPreferenceWithAdminCheck(PrefsDialog.AUTO_PUSH, autoPush, MainFrame.this, bundle);
				boolean sourceDaysSuccess = Utils.putIntPreferenceWithAdminCheck(PrefsDialog.AUTO_REMOVE_SOURCE, autoDeleteSourceDays, MainFrame.this, bundle);
				boolean targetDaysSuccess = Utils.putIntPreferenceWithAdminCheck(PrefsDialog.AUTO_REMOVE_TARGET, autoDeleteTargetDays, MainFrame.this, bundle);
				boolean manualTargetSuccess = Utils.putBooleanPreferenceWithAdminCheck(PrefsDialog.MANUAL_TARGET, manualTgt, MainFrame.this, bundle);
				boolean loadPeriodSuccess = Utils.putIntPreferenceWithAdminCheck(PrefsDialog.LOAD_PERIOD, loadPeriod, MainFrame.this, bundle);
				boolean loadNumberSuccess = Utils.putIntPreferenceWithAdminCheck(PrefsDialog.LOAD_NUMBER, loadNumber, MainFrame.this, bundle);
				boolean overwriteDiscSuccess = Utils.putDoublePreferenceWithAdminCheck(PrefsDialog.OVERWRITE_DISCOUNT, overwriteDisc, MainFrame.this, bundle);
				boolean fixMCSuccess = Utils.putBooleanPreferenceWithAdminCheck(PrefsDialog.FIX_M_C, fixMC, MainFrame.this, bundle);
				
				if (!autoPushSuccess || !sourceDaysSuccess || !targetDaysSuccess || !manualTargetSuccess || 
					!loadPeriodSuccess || !loadNumberSuccess || !overwriteDiscSuccess || !fixMCSuccess) {
					log.warn("Failed to save some application preferences");
				}
				
				refreshPrefs();
			}

			public void partsTypePreferencesSet() {
				PartsTypeHelper.getTypes().stream().forEach(t -> {
					if (t.isAllowKeepType()) {
						boolean success = Utils.putBooleanPreferenceWithAdminCheck(PrefsDialog.PARTS_TYPE_PREFIX + t.getId(), t.isKeepType(), MainFrame.this, bundle);
						if (!success) {
							log.warn("Failed to save parts type preference: {} = {}", t.getId(), t.isKeepType());
						}
					}
				});
			}
		});

		this.setTitle(bundle.getString("tt_title") + " " + PkVersion.version.toString() + "  -  " + ClientHelper.getClient().getClientName());
		this.setIconImage(Utils.getCommonIcon(getClass()).getImage());

		emsToolbar = new EmsToolbar();
		estPanel = new EstimatePanel(this);

		setLayout(new BorderLayout());

		setJMenuBar(createMenuBar(exitListener, bundle));

		add(emsToolbar, BorderLayout.PAGE_START);
		add(estPanel, BorderLayout.CENTER);

		emsToolbar.setEmsFilterListener(estPanel);
		estPanel.setEmsToolBarListener(emsToolbar);
		setMinimumSize(new Dimension(500, 400));
//		setExtendedState(getExtendedState() | JFrame.MAXIMIZED_BOTH);
		setSize(900, 700);
		setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
		setLocationRelativeTo(null);
		setVisible(true);

		estPanel.loadEstimations(removeEmsDays, removeProcessedDays);
		emsFileService = new EmsFileService(prefs);
		Global.setEmsFileService(emsFileService);

		PartsParameters parameters = new PartsParameters();
		try {
			parameters = Global.httpService.getPUParameters(Global.clientId, Global.machineId);
		} catch (Exception e) {
//					throw new RuntimeException(e);
		}

		Global.partsParameters = parameters;

		Runnable task = () -> {
//			log.info("processing estimations...");
			try {
				PartsParameters params = Global.httpService.getPUParameters(Global.clientId, Global.machineId);
				if (Global.enableDebug) {
					Utils.buildDebug(Global.PU_TASK, HttpService.mapper.writeValueAsString(params));
				}
				if (params.getDoJob() > 0) {
					Global.getEmsFileService().processEstimations(params);
				}
				Global.httpService.sendDebug(Global.clientId, Global.machineId, Global.debugMap.get(Global.PU_TASK));
                if (Global.debugMap.containsKey(Global.PU_TASK) && Global.debugMap.get(Global.PU_TASK) != null && Global.debugMap.get(Global.PU_TASK).getEvents() != null) {
                    Global.debugMap.get(Global.PU_TASK).getEvents().clear();
                }
			} catch (Exception e) {
				log.error(e.getMessage());
			}
		};

		ScheduledExecutorService executorService = Executors
				.newSingleThreadScheduledExecutor();

		executorService.scheduleWithFixedDelay(task, parameters.getStartDelayInMinutes(), parameters.getIntervalInMinutes(), TimeUnit.MINUTES);

		// Check and show promo after frame is visible
		SwingUtilities.invokeLater(() -> {
			initializeMessageScheduler();
		});
	}

	private void refreshPrefs() {
		removeEmsDays = prefs.getInt(PrefsDialog.AUTO_REMOVE_SOURCE, 1);
		removeProcessedDays = prefs.getInt(PrefsDialog.AUTO_REMOVE_TARGET, 1);
		int loadPeriod = prefs.getInt(PrefsDialog.LOAD_PERIOD, 1);
		int loadNumber = prefs.getInt(PrefsDialog.LOAD_NUMBER, 0);
		Global.defaultShopDiscount = prefs.getDouble(PrefsDialog.OVERWRITE_DISCOUNT, Global.defaultShopDiscount);
		Global.loadPeriod = loadPeriod;
		Global.loadNumber = loadNumber;
		Global.fixMtoC = prefs.getBoolean(PrefsDialog.FIX_M_C, false);
	}

	private JMenuBar createMenuBar(ActionListener exitListener, ResourceBundle bundle) {
		JMenuBar menuBar = new JMenuBar();

		JMenu fileMenu = new JMenu(bundle.getString("mu_file"));
		JMenu inventoryMenu = new JMenu(bundle.getString("mu_inventory"));
		JMenuItem regMenu = new JMenuItem(bundle.getString("mu_registration"));
		JMenuItem exitItem = new JMenuItem(bundle.getString("mu_exit"));

		fileMenu.add(regMenu);
		fileMenu.addSeparator();
		fileMenu.add(exitItem);

		JMenuItem inventorySettings = new JMenuItem(bundle.getString("mu_inventory_settings"));
		JMenuItem partsList = new JMenuItem(bundle.getString("mu_parts_list"));
		JMenuItem invoiceList = new JMenuItem(bundle.getString("mu_invoice_list"));

		Global.portalUrl = propertyLoader.getProperties().getProperty("portal.url");
		Global.portalApiUrl = propertyLoader.getProperties().getProperty("portal.api.url");
		String portalUrl = Global.portalUrl + "/desktop/login?client="
                + URLEncoder.encode(Global.clientId, StandardCharsets.UTF_8) + "&machine=" + URLEncoder.encode(Global.machineId, StandardCharsets.UTF_8)
                + "&page=inventory";
		partsList.addActionListener(new ActionListener() {
			@Override
			public void actionPerformed(ActionEvent e) {
				if (Desktop.isDesktopSupported() && Desktop.getDesktop().isSupported(Desktop.Action.BROWSE)) {
					try {
						Desktop.getDesktop().browse(new URI(portalUrl));
					} catch (IOException | URISyntaxException ex) {
						ex.printStackTrace();
					}
				}
			}
		});
		inventoryMenu.add(inventorySettings);
		inventoryMenu.add(partsList);
		inventoryMenu.add(invoiceList);

		JMenu windowMenu = new JMenu(bundle.getString("mu_window"));
		JMenuItem prefsItem = new JMenuItem(bundle.getString("mu_prefs"));

		windowMenu.add(prefsItem);

		menuBar.add(fileMenu);
		menuBar.add(inventoryMenu);
		menuBar.add(windowMenu);

		regMenu.addActionListener(new ActionListener() {
			@Override
			public void actionPerformed(ActionEvent e) {
				registDialog.setDefaults(regInfo.get(RegistDialog.CLIENT, Utils.getUUIDFromClipboard()), regInfo.get(RegistDialog.STATION, Utils.getHostname()));
				registDialog.setVisible(true);
			}
		});

		inventorySettings.addActionListener(new ActionListener() {
			@Override
			public void actionPerformed(ActionEvent e) {
				InvoiceSettingsDialog dialog = new InvoiceSettingsDialog(MainFrame.this, bundle.getString("tt_inventory_settings"), bundle);
				dialog.setVisible(true);
			}
		});

		invoiceList.addActionListener(new ActionListener() {
			@Override
			public void actionPerformed(ActionEvent e) {
				InvoiceHistoryDialog dialog = new InvoiceHistoryDialog(MainFrame.this, bundle.getString("lb_history"));
				dialog.setVisible(true);
			}
		});
		
		prefsItem.addActionListener(new ActionListener() {
			public void actionPerformed(ActionEvent e) {
				prefsDialog.setVisible(true);
			}
		});

		fileMenu.setMnemonic(KeyEvent.VK_F);
		exitItem.setMnemonic(KeyEvent.VK_X);

		inventoryMenu.setMnemonic(KeyEvent.VK_I);

		prefsItem.setAccelerator(KeyStroke.getKeyStroke(KeyEvent.VK_P,
				ActionEvent.CTRL_MASK));

		exitItem.setAccelerator(KeyStroke.getKeyStroke(KeyEvent.VK_X,
				ActionEvent.CTRL_MASK));
		
		exitItem.addActionListener(exitListener);

		return menuBar;
	}

	private void migrateSources(List<EmsSource> newSources, List<EmsSource> oldSources) {
		for (EmsSource newSource : newSources) {
			for (EmsSource oldSource : oldSources) {
				if (newSource.getName().equals(oldSource.getName())) {
					String oldExportPath = oldSource.getExportPath();
					String oldTraderPath = oldSource.getTraderPath();
					if (StringUtils.isNotBlank(oldExportPath) || StringUtils.isNotBlank(oldTraderPath)) {
						String key = newSource.getName();
						boolean exportSuccess = Utils.putPreferenceWithAdminCheck(key + PrefsDialog.EXPORT_PATH, oldExportPath, this, bundle);
						boolean traderSuccess = Utils.putPreferenceWithAdminCheck(key + PrefsDialog.TRADER_PATH, oldTraderPath, this, bundle);
						if (!exportSuccess || !traderSuccess) {
							log.warn("Failed to migrate source paths for: {}", key);
						}
					}
					break;
				}
			}
		}
		
		boolean success = Utils.putBooleanPreferenceWithAdminCheck("migrated", true, this, bundle);
		if (!success) {
			log.warn("Failed to set migration flag");
		}
	}

	private void initializeMessageScheduler() {
		if (messageSettings != null) {
			// Create message callback that centers the main window
			MessagePopupDialog.MessageCallback callback = new MessagePopupDialog.MessageCallback() {
				@Override
				public void onDoNotShowAgainClicked(String title) {
					handleDoNotShowAgain(title);
					centerWindow(); // Re-center main window
				}

				@Override
				public void onMessageWindowClosed() {
					centerWindow(); // Re-center main window
				}
			};

			// Create and start the message scheduler
			messageScheduler = new MessageScheduler(this, messageSettings, prefs, callback);
			messageScheduler.startScheduler();
		}
	}

	private void handleDoNotShowAgain(String title) {
		long currentTime = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
		boolean success = Utils.putLongPreferenceWithAdminCheck(PrefsDialog.MESSAGE_STOPPED_AT, currentTime, this, bundle);
		if (!success) {
			log.warn("Failed to save message stop time preference");
		}
	}

	private void centerWindow() {
		GraphicsConfiguration gc = getGraphicsConfiguration();
		Rectangle screenBounds = gc.getBounds();
		setLocation(
				screenBounds.x + (screenBounds.width - getWidth()) / 2,
				screenBounds.y + (screenBounds.height - getHeight()) / 2
		);
	}
}
