<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.2.final using JasperReports Library version 6.21.2-8434a0bd7c3bbc37cbf916f2968d35e4b165821a  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd"
              name="PartsInvoice" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="20" rightMargin="20"
              topMargin="20" bottomMargin="20" uuid="4eedbb89-b4f6-4469-9ab6-f642a1688cf7">
    <property name="com.jaspersoft.studio.data.defaultdataadapter" value="ordered-parts"/>
    <style name="Title" forecolor="#FFFFFF" fontName="Times New Roman" fontSize="50" isBold="false"
           pdfFontName="Times-Bold"/>
    <style name="SubTitle" forecolor="#CCCCCC" fontName="Times New Roman" fontSize="18" isBold="false"
           pdfFontName="Times-Roman"/>
    <style name="Column header" forecolor="#666666" fontName="Times New Roman" fontSize="14" isBold="true"/>
    <style name="Detail" mode="Transparent" fontName="Times New Roman"/>
    <style name="Row" mode="Transparent" fontName="Times New Roman" pdfFontName="Times-Roman">
        <conditionalStyle>
            <conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
            <style mode="Opaque" backcolor="#EEEFF0"/>
        </conditionalStyle>
    </style>
    <style name="Table">
        <box>
            <pen lineWidth="1.0" lineColor="#000000"/>
            <topPen lineWidth="1.0" lineColor="#000000"/>
            <leftPen lineWidth="1.0" lineColor="#000000"/>
            <bottomPen lineWidth="1.0" lineColor="#000000"/>
            <rightPen lineWidth="1.0" lineColor="#000000"/>
        </box>
    </style>
    <style name="Table_TH" mode="Opaque" backcolor="#FFFFFF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
            <topPen lineWidth="0.5" lineColor="#000000"/>
            <leftPen lineWidth="0.5" lineColor="#000000"/>
            <bottomPen lineWidth="0.5" lineColor="#000000"/>
            <rightPen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="Table_CH" mode="Opaque" backcolor="#CACED0">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
            <topPen lineWidth="0.5" lineColor="#000000"/>
            <leftPen lineWidth="0.5" lineColor="#000000"/>
            <bottomPen lineWidth="0.5" lineColor="#000000"/>
            <rightPen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
            <topPen lineWidth="0.5" lineColor="#000000"/>
            <leftPen lineWidth="0.5" lineColor="#000000"/>
            <bottomPen lineWidth="0.5" lineColor="#000000"/>
            <rightPen lineWidth="0.5" lineColor="#000000"/>
        </box>
        <conditionalStyle>
            <conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
            <style backcolor="#D8D8D8"/>
        </conditionalStyle>
    </style>
    <subDataset name="tableDataset" uuid="f13e6d36-5148-4ecc-bbe3-3035def80980">
        <queryString>
            <![CDATA[]]>
        </queryString>
    </subDataset>
    <parameter name="companyName" class="java.lang.String"/>
    <parameter name="companyAddress" class="java.lang.String"/>
    <parameter name="companyCPZ" class="java.lang.String"/>
    <parameter name="companyPE" class="java.lang.String"/>
    <parameter name="invoiceDate" class="java.lang.String"/>
    <parameter name="invoiceNumber" class="java.lang.String"/>
    <parameter name="billToName" class="java.lang.String"/>
    <parameter name="billToAddress" class="java.lang.String"/>
    <parameter name="billToCPZ" class="java.lang.String"/>
    <parameter name="shipToName" class="java.lang.String"/>
    <parameter name="shipToAddress" class="java.lang.String"/>
    <parameter name="shipToCPZ" class="java.lang.String"/>
    <parameter name="taxSummary" class="java.lang.String"/>
    <parameter name="taxTotal" class="java.lang.String"/>
    <parameter name="amountTotal" class="java.lang.String"/>
    <parameter name="taxId" class="java.lang.String"/>
    <queryString>
        <![CDATA[]]>
    </queryString>
    <field name="description" class="java.lang.String"/>
    <field name="quantity" class="java.lang.Integer"/>
    <field name="price" class="java.lang.Double"/>
    <field name="amount" class="java.lang.Double"/>
    <background>
        <band height="580" splitType="Stretch">
            <line>
                <reportElement x="280" y="220" width="1" height="330" uuid="01f3d5fc-9d23-4c7b-96cf-0ff7b3285400">
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
            </line>
            <line>
                <reportElement x="350" y="220" width="1" height="330" uuid="01f3d5fc-9d23-4c7b-96cf-0ff7b3285400">
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
            </line>
            <line>
                <reportElement x="440" y="220" width="1" height="330" uuid="01f3d5fc-9d23-4c7b-96cf-0ff7b3285400">
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
            </line>
            <line>
                <reportElement x="1" y="549" width="530" height="1" uuid="4494e946-4ff5-4d57-b3eb-9d64eda302c4"/>
            </line>
            <staticText>
                <reportElement x="45" y="316" width="455" height="194" forecolor="#BDBBBB"
                               uuid="0a40e00a-4ee0-411a-8ead-7e2a6fab42b1"/>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font size="99" isBold="true" isItalic="true"/>
                </textElement>
                <text><![CDATA[VOID]]></text>
            </staticText>
        </band>
    </background>
    <title>
        <band height="196" splitType="Stretch">
            <staticText>
                <reportElement x="0" y="90" width="84" height="25" uuid="57aed2b1-4f4e-40a3-a6ad-54dae8dd4c5a">
                    <property name="local_mesure_unitheight" value="pixel"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <textElement>
                    <font size="16" isBold="true"/>
                </textElement>
                <text><![CDATA[Bill to:]]></text>
            </staticText>
            <textField>
                <reportElement x="0" y="115" width="260" height="15" uuid="872f5911-786f-4785-b8e0-2de3ff749f48"/>
                <textFieldExpression><![CDATA[$P{billToName}]]></textFieldExpression>
            </textField>
            <line>
                <reportElement x="0" y="80" width="556" height="1" uuid="806ce5df-1219-4876-ae0c-ca7405b1f246">
                    <property name="local_mesure_unitheight" value="pixel"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
            </line>
            <textField>
                <reportElement x="0" y="130" width="260" height="15" uuid="80e17930-2ec9-4980-97b4-5db7b757e9b7"/>
                <textFieldExpression><![CDATA[$P{billToAddress}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="0" y="145" width="260" height="15" uuid="8dd1db95-3857-4ae4-8661-fe66829793f8"/>
                <textFieldExpression><![CDATA[$P{billToCPZ}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="310" y="144" width="230" height="15" uuid="14c799d6-c66b-49c5-af4a-2426d5e037fe"/>
                <textFieldExpression><![CDATA[$P{shipToCPZ}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="310" y="114" width="230" height="15" uuid="8532fbfa-cd51-49d6-bb5f-6e5bf4b28f9d"/>
                <textFieldExpression><![CDATA[$P{shipToName}]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement x="310" y="89" width="84" height="25" uuid="139ebe8d-25b0-411e-986c-************">
                    <property name="local_mesure_unitheight" value="pixel"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <textElement>
                    <font size="16" isBold="true"/>
                </textElement>
                <text><![CDATA[Ship to:]]></text>
            </staticText>
            <textField>
                <reportElement x="310" y="129" width="230" height="15" uuid="77cb1d07-cf3f-4d5b-ae13-233dd7367651"/>
                <textFieldExpression><![CDATA[$P{shipToAddress}]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement x="398" y="10" width="50" height="20" uuid="0f86baff-6386-4f3f-b3fe-2388707babe8"/>
                <box rightPadding="4"/>
                <textElement textAlignment="Right"/>
                <text><![CDATA[Date:]]></text>
            </staticText>
            <textField pattern="EEEEE dd MMMMM yyyy">
                <reportElement x="448" y="10" width="84" height="20" uuid="bb10dbe1-0a4f-4722-9953-c163b63cf979"/>
                <textFieldExpression><![CDATA[$P{invoiceDate}]]></textFieldExpression>
            </textField>
            <textField pattern="EEEEE dd MMMMM yyyy">
                <reportElement x="448" y="30" width="84" height="20" uuid="3836ce65-eca3-4cad-a6de-b1818def0a2b"/>
                <textFieldExpression><![CDATA[$P{invoiceNumber}]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement x="398" y="30" width="50" height="20" uuid="0b3f9342-da78-4cfa-9fc5-2301c4749678"/>
                <box rightPadding="4"/>
                <textElement textAlignment="Right"/>
                <text><![CDATA[Invoice #]]></text>
            </staticText>
            <textField>
                <reportElement x="0" y="0" width="380" height="25" uuid="087aed76-354e-4d38-b672-6e482f1a0baa">
                    <property name="local_mesure_unitheight" value="pixel"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <textElement>
                    <font size="19" isBold="true"/>
                </textElement>
                <textFieldExpression><![CDATA[$P{companyName}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="0" y="55" width="380" height="15" uuid="7ed52503-53b5-47dd-ad06-bac8d3bdd9a6"/>
                <textFieldExpression><![CDATA[$P{companyPE}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="0" y="25" width="380" height="15" uuid="e9ebb1c4-24e2-4a46-8612-ce62694fbfd7"/>
                <textFieldExpression><![CDATA[$P{companyAddress}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="0" y="40" width="380" height="15" uuid="1f3d9246-1017-4099-b908-463daf9f4147"/>
                <textFieldExpression><![CDATA[$P{companyCPZ}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="448" y="50" width="100" height="20" uuid="c93c0d9f-8621-42e4-ab27-7d85a300c9a1">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <textFieldExpression><![CDATA[$P{taxId}]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement x="398" y="50" width="50" height="20" uuid="fcde48c5-36be-45f9-8c5d-3f4e469e5b67"/>
                <box rightPadding="4"/>
                <textElement textAlignment="Right"/>
                <text><![CDATA[Tax ID:]]></text>
            </staticText>
        </band>
    </title>
    <columnHeader>
        <band height="28">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <staticText>
                <reportElement x="0" y="5" width="260" height="15" uuid="fd878fae-ce92-423e-be3f-689aee18e218">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <textElement textAlignment="Center"/>
                <text><![CDATA[Description]]></text>
            </staticText>
            <staticText>
                <reportElement x="290" y="5" width="50" height="15" uuid="7ac3336a-3257-4334-b829-f27e3fefdc84">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <textElement textAlignment="Center"/>
                <text><![CDATA[Qty]]></text>
            </staticText>
            <staticText>
                <reportElement x="360" y="5" width="70" height="15" uuid="f133b330-726b-4d66-aba1-026d6a4732f4">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <textElement textAlignment="Center"/>
                <text><![CDATA[Unit Price]]></text>
            </staticText>
            <staticText>
                <reportElement x="460" y="5" width="70" height="15" uuid="8320e373-d874-47ec-983e-d00404e09aa0">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <textElement textAlignment="Center"/>
                <text><![CDATA[Amount]]></text>
            </staticText>
            <line>
                <reportElement x="1" y="23" width="530" height="1" uuid="9c7dfe52-f8f9-457b-b4c4-c963093723cd">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
            </line>
        </band>
    </columnHeader>
    <detail>
        <band height="40">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
            <textField>
                <reportElement x="0" y="0" width="280" height="40" uuid="b855ffb5-386c-4dd1-81f2-4e1e7dbe7f92">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="300" y="0" width="40" height="20" uuid="fc8dee8a-a1f2-4dce-98f9-9da4046a720e">
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="pixel"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <textElement textAlignment="Right"/>
                <textFieldExpression><![CDATA[$F{quantity}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="360" y="0" width="70" height="20" uuid="1071a613-c71c-4b1f-8a5d-8aaf6d7d6640">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <textElement textAlignment="Right"/>
                <textFieldExpression><![CDATA[$F{price}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="464" y="0" width="67" height="20" uuid="be41dbe0-539e-4c1e-a5c9-22fbc56313ae">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <textElement textAlignment="Right"/>
                <textFieldExpression><![CDATA[$F{amount}]]></textFieldExpression>
            </textField>
        </band>
    </detail>
    <columnFooter>
        <band height="80">
            <staticText>
                <reportElement style="Detail" x="1" y="14" width="130" height="20"
                               uuid="d70379f3-e13f-485a-95ba-c018df738d69"/>
                <text><![CDATA[Sales Tax Summary]]></text>
            </staticText>
            <staticText>
                <reportElement x="350" y="15" width="100" height="18" uuid="c0f423ba-7496-4dad-a465-68b5674f85dc"/>
                <textElement verticalAlignment="Middle"/>
                <text><![CDATA[Sales Tax Total]]></text>
            </staticText>
            <line>
                <reportElement x="350" y="34" width="180" height="1" uuid="75f96fff-207d-49db-91d7-2194da75df12">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
            </line>
            <staticText>
                <reportElement x="350" y="41" width="100" height="18" uuid="2264d9a1-7291-4f1c-bce1-ff5dffae49d4"/>
                <textElement verticalAlignment="Middle"/>
                <text><![CDATA[Total]]></text>
            </staticText>
            <textField>
                <reportElement x="10" y="34" width="100" height="46" uuid="d31a2819-936e-4dbe-8d03-d69f38a4b2e8"/>
                <textElement textAlignment="Left" verticalAlignment="Top">
                    <font size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$P{taxSummary}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="467" y="15" width="64" height="18" uuid="1ec3c02a-feff-4e6a-88a5-37c52bf068cd"/>
                <textElement textAlignment="Right"/>
                <textFieldExpression><![CDATA[$P{taxTotal}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="468" y="40" width="63" height="19" uuid="6df9661a-b1a1-4d17-a403-26c5fa20b57f"/>
                <textElement textAlignment="Right"/>
                <textFieldExpression><![CDATA[$P{amountTotal}]]></textFieldExpression>
            </textField>
        </band>
    </columnFooter>
    <lastPageFooter>
        <band height="177">
            <property name="com.jaspersoft.studio.layout"
                      value="com.jaspersoft.studio.editor.layout.spreadsheet.SpreadsheetLayout"/>
        </band>
    </lastPageFooter>
</jasperReport>
